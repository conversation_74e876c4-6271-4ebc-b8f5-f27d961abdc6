// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fuel_status_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FuelStatusModel _$FuelStatusModelFromJson(Map<String, dynamic> json) =>
    FuelStatusModel(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      currentLevel: (json['currentLevel'] as num).toDouble(),
      capacity: (json['capacity'] as num).toDouble(),
      unit: json['unit'] as String,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      lastUpdatedBy: json['lastUpdatedBy'] as String?,
      consumptionRate: (json['consumptionRate'] as num?)?.toDouble(),
      estimatedEmptyDate: json['estimatedEmptyDate'] == null
          ? null
          : DateTime.parse(json['estimatedEmptyDate'] as String),
      status: json['status'] as String,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$FuelStatusModelToJson(FuelStatusModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'currentLevel': instance.currentLevel,
      'capacity': instance.capacity,
      'unit': instance.unit,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'lastUpdatedBy': instance.lastUpdatedBy,
      'consumptionRate': instance.consumptionRate,
      'estimatedEmptyDate': instance.estimatedEmptyDate?.toIso8601String(),
      'status': instance.status,
      'images': instance.images,
      'notes': instance.notes,
      'metadata': instance.metadata,
    };
