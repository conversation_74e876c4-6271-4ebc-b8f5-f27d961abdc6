class AppConfig {
  static const String appName = 'Property Management';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://your-backend-api.com/api';
  static const String devBaseUrl = 'http://localhost:3000/api';
  static const String stagingBaseUrl = 'https://staging-api.com/api';
  
  // Environment
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
  
  static String get apiBaseUrl {
    switch (environment) {
      case 'production':
        return baseUrl;
      case 'staging':
        return stagingBaseUrl;
      default:
        return devBaseUrl;
    }
  }
  
  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String biometricEnabledKey = 'biometric_enabled';
  
  // Hive Box Names
  static const String authBoxName = 'auth_box';
  static const String propertyBoxName = 'property_box';
  static const String fuelBoxName = 'fuel_box';
  static const String maintenanceBoxName = 'maintenance_box';
  static const String attendanceBoxName = 'attendance_box';
  static const String ottBoxName = 'ott_box';
  
  // Notification Channels
  static const String criticalAlertsChannel = 'critical_alerts';
  static const String maintenanceChannel = 'maintenance';
  static const String generalChannel = 'general';
  
  // Location Settings
  static const double locationAccuracy = 10.0; // meters
  static const int locationTimeoutSeconds = 30;
  
  // Image Settings
  static const int maxImageSize = 2048; // pixels
  static const int imageQuality = 85; // percentage
  
  // Sync Settings
  static const int syncIntervalMinutes = 15;
  static const int maxRetryAttempts = 3;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
}
