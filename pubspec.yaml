name: property_management_app
description: A comprehensive Flutter mobile application for Property Management System
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5

  # API Integration
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2

  # Authentication
  flutter_secure_storage: ^9.0.0
  jwt_decoder: ^2.0.1

  # UI Components
  cupertino_icons: ^1.0.2
  material_design_icons_flutter: ^7.0.7296
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0

  # Camera & Media
  camera: ^0.10.5+5
  image_picker: ^1.0.4
  permission_handler: ^11.0.1

  # Location & Maps
  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.0

  # Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.6
  flutter_local_notifications: ^16.2.0

  # Connectivity
  connectivity_plus: ^5.0.1

  # QR/Barcode (temporarily removed for compatibility)
  # qr_code_scanner: ^1.0.1

  # Charts & Analytics
  fl_chart: ^0.64.0

  # Utilities
  intl: ^0.18.1
  path_provider: ^2.1.1
  url_launcher: ^6.2.1
  dartz: ^0.10.1
  get_it: ^7.6.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/

  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700
