import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/property_repository.dart';
import '../../data/models/property_model.dart';

class GetPropertyDetailsUseCase implements UseCase<PropertyModel, GetPropertyDetailsParams> {
  final PropertyRepository repository;

  GetPropertyDetailsUseCase(this.repository);

  @override
  Future<Either<Failure, PropertyModel>> call(GetPropertyDetailsParams params) async {
    return await repository.getPropertyDetails(params.propertyId);
  }
}

class GetPropertyDetailsParams extends Equatable {
  final String propertyId;

  const GetPropertyDetailsParams({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}
