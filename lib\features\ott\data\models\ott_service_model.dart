import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'ott_service_model.g.dart';

@JsonSerializable()
class OttServiceModel extends Equatable {
  final String id;
  final String propertyId;
  final String serviceName;
  final String provider;
  final String status;
  final String? ipAddress;
  final int? port;
  final String? username;
  final DateTime? lastChecked;
  final bool isActive;
  final Map<String, dynamic>? configuration;
  final Map<String, dynamic>? metadata;

  const OttServiceModel({
    required this.id,
    required this.propertyId,
    required this.serviceName,
    required this.provider,
    required this.status,
    this.ipAddress,
    this.port,
    this.username,
    this.lastChecked,
    required this.isActive,
    this.configuration,
    this.metadata,
  });

  factory OttServiceModel.fromJson(Map<String, dynamic> json) =>
      _$OttServiceModelFromJson(json);

  Map<String, dynamic> toJson() => _$OttServiceModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        propertyId,
        serviceName,
        provider,
        status,
        ipAddress,
        port,
        username,
        lastChecked,
        isActive,
        configuration,
        metadata,
      ];
}
