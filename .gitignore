# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/Runner/GeneratedPluginRegistrant.*
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# iOS related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/ephemeral/
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*

# Coverage
coverage/

# Exceptions to above rules.
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages

# Firebase
**/android/app/google-services.json
**/ios/Runner/GoogleService-Info.plist
**/ios/firebase_app_id_file.json
**/lib/firebase_options.dart

# Environment files
.env
.env.local
.env.development
.env.staging
.env.production

# API Keys and secrets
**/lib/core/config/api_keys.dart
**/android/key.properties
**/ios/Runner/Config.xcconfig

# Generated files
**/*.g.dart
**/*.freezed.dart
**/generated_plugin_registrant.dart

# Build artifacts
**/build/
**/dist/

# Temporary files
*.tmp
*.temp

# Log files
*.log

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
node_modules/

# IDE files
.vscode/settings.json
.vscode/launch.json
*.code-workspace

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Archives
*.zip
*.tar.gz
*.rar

# Backup files
*.bak
*.backup
*~

# Test coverage
lcov.info
coverage/
test/coverage/

# Documentation
doc/api/

# Local configuration
local.properties

# Keystore files
*.jks
*.keystore

# Fastlane
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Web related
web/

# Windows related
*.exe
*.dll
*.pdb

# macOS related
*.dSYM/

# Linux related
*.so

# Flutter Web
/web/

# Flutter Desktop
/windows/
/linux/
/macos/
