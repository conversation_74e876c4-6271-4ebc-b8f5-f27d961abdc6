<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Property Management</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>property_management_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- Location permissions -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to verify your presence at work sites and for attendance tracking.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>This app needs location access to verify your presence at work sites and for attendance tracking.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs location access to verify your presence at work sites and for attendance tracking.</string>
	
	<!-- Camera permissions -->
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to take photos for maintenance reports and fuel level documentation.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photo library access to select images for reports and documentation.</string>
	
	<!-- Microphone permission (if needed for video recording) -->
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs microphone access for video recording features.</string>
	
	<!-- Face ID permission -->
	<key>NSFaceIDUsageDescription</key>
	<string>This app uses Face ID for secure and convenient authentication.</string>
	
	<!-- Background modes -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-fetch</string>
		<string>background-processing</string>
		<string>location</string>
	</array>
	
	<!-- Firebase configuration -->
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	
</dict>
</plist>
