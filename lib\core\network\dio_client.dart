import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../config/app_config.dart';
import '../error/exceptions.dart';

class DioClient {
  static Dio createDio() {
    final dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    dio.interceptors.add(AuthInterceptor());
    dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) {
        // Only log in debug mode
        if (AppConfig.environment == 'development') {
          print(object);
        }
      },
    ));
    dio.interceptors.add(ErrorInterceptor());

    return dio;
  }
}

class AuthInterceptor extends Interceptor {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final token = await _secureStorage.read(key: AppConfig.accessTokenKey);
    if (token != null) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == 401) {
      // Token expired, try to refresh
      final refreshToken = await _secureStorage.read(key: AppConfig.refreshTokenKey);
      if (refreshToken != null) {
        try {
          final response = await Dio().post(
            '${AppConfig.apiBaseUrl}/auth/refresh',
            data: {'refreshToken': refreshToken},
          );
          
          final newToken = response.data['accessToken'];
          await _secureStorage.write(key: AppConfig.accessTokenKey, value: newToken);
          
          // Retry the original request
          final requestOptions = err.requestOptions;
          requestOptions.headers['Authorization'] = 'Bearer $newToken';
          
          final retryResponse = await Dio().fetch(requestOptions);
          handler.resolve(retryResponse);
          return;
        } catch (e) {
          // Refresh failed, clear tokens and redirect to login
          await _secureStorage.delete(key: AppConfig.accessTokenKey);
          await _secureStorage.delete(key: AppConfig.refreshTokenKey);
        }
      }
    }
    handler.next(err);
  }
}

class ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    ServerException exception;
    
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        exception = ServerException(
          message: 'Connection timeout. Please check your internet connection.',
          statusCode: 408,
        );
        break;
      case DioExceptionType.badResponse:
        final statusCode = err.response?.statusCode ?? 500;
        final message = err.response?.data?['message'] ?? 
                       err.response?.statusMessage ?? 
                       'An error occurred';
        exception = ServerException(
          message: message,
          statusCode: statusCode,
        );
        break;
      case DioExceptionType.cancel:
        exception = ServerException(
          message: 'Request was cancelled',
          statusCode: 499,
        );
        break;
      case DioExceptionType.connectionError:
        exception = ServerException(
          message: 'No internet connection. Please check your network.',
          statusCode: 503,
        );
        break;
      default:
        exception = ServerException(
          message: 'An unexpected error occurred',
          statusCode: 500,
        );
    }
    
    handler.reject(DioException(
      requestOptions: err.requestOptions,
      error: exception,
      type: err.type,
      response: err.response,
    ));
  }
}
