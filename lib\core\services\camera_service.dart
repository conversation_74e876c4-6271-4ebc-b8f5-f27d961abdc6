import 'dart:io';
import 'dart:typed_data';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart';

import '../config/app_config.dart';
import '../error/exceptions.dart';
import '../utils/image_utils.dart';

class CameraService {
  static List<CameraDescription>? _cameras;
  static CameraController? _controller;
  final ImagePicker _picker = ImagePicker();

  static Future<void> initializeCameras() async {
    try {
      _cameras = await availableCameras();
    } catch (e) {
      throw CameraException('Failed to initialize cameras: $e');
    }
  }

  Future<bool> requestCameraPermission() async {
    final permission = await Permission.camera.request();
    return permission.isGranted;
  }

  Future<bool> requestStoragePermission() async {
    final permission = await Permission.storage.request();
    return permission.isGranted;
  }

  Future<bool> isCameraPermissionGranted() async {
    final permission = await Permission.camera.status;
    return permission.isGranted;
  }

  Future<bool> isStoragePermissionGranted() async {
    final permission = await Permission.storage.status;
    return permission.isGranted;
  }

  Future<CameraController> initializeCamera({
    CameraLensDirection direction = CameraLensDirection.back,
  }) async {
    if (_cameras == null || _cameras!.isEmpty) {
      await initializeCameras();
    }

    if (_cameras == null || _cameras!.isEmpty) {
      throw CameraException('No cameras available');
    }

    // Check permissions
    if (!await isCameraPermissionGranted()) {
      final granted = await requestCameraPermission();
      if (!granted) {
        throw CameraException('Camera permission denied');
      }
    }

    // Find camera with specified direction
    final camera = _cameras!.firstWhere(
      (camera) => camera.lensDirection == direction,
      orElse: () => _cameras!.first,
    );

    _controller = CameraController(
      camera,
      ResolutionPreset.high,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    try {
      await _controller!.initialize();
      return _controller!;
    } catch (e) {
      throw CameraException('Failed to initialize camera: $e');
    }
  }

  Future<File> takePicture() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw CameraException('Camera not initialized');
    }

    try {
      final XFile image = await _controller!.takePicture();
      final File imageFile = File(image.path);
      
      // Compress and resize image
      final compressedFile = await ImageUtils.compressImage(
        imageFile,
        maxWidth: AppConfig.maxImageSize,
        maxHeight: AppConfig.maxImageSize,
        quality: AppConfig.imageQuality,
      );
      
      return compressedFile;
    } catch (e) {
      throw CameraException('Failed to take picture: $e');
    }
  }

  Future<File?> pickImageFromGallery() async {
    try {
      if (!await isStoragePermissionGranted()) {
        final granted = await requestStoragePermission();
        if (!granted) {
          throw CameraException('Storage permission denied');
        }
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: AppConfig.maxImageSize.toDouble(),
        maxHeight: AppConfig.maxImageSize.toDouble(),
        imageQuality: AppConfig.imageQuality,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw CameraException('Failed to pick image from gallery: $e');
    }
  }

  Future<File?> pickImageFromCamera() async {
    try {
      if (!await isCameraPermissionGranted()) {
        final granted = await requestCameraPermission();
        if (!granted) {
          throw CameraException('Camera permission denied');
        }
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: AppConfig.maxImageSize.toDouble(),
        maxHeight: AppConfig.maxImageSize.toDouble(),
        imageQuality: AppConfig.imageQuality,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      throw CameraException('Failed to pick image from camera: $e');
    }
  }

  Future<List<File>> pickMultipleImages() async {
    try {
      if (!await isStoragePermissionGranted()) {
        final granted = await requestStoragePermission();
        if (!granted) {
          throw CameraException('Storage permission denied');
        }
      }

      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: AppConfig.maxImageSize.toDouble(),
        maxHeight: AppConfig.maxImageSize.toDouble(),
        imageQuality: AppConfig.imageQuality,
      );

      return images.map((image) => File(image.path)).toList();
    } catch (e) {
      throw CameraException('Failed to pick multiple images: $e');
    }
  }

  Future<File> saveImageToAppDirectory(File imageFile, String fileName) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${directory.path}/images');
      
      if (!await imagesDir.exists()) {
        await imagesDir.create(recursive: true);
      }

      final savedFile = File('${imagesDir.path}/$fileName');
      return await imageFile.copy(savedFile.path);
    } catch (e) {
      throw CameraException('Failed to save image: $e');
    }
  }

  Future<File> saveImageWithTimestamp(File imageFile, String prefix) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final fileName = '${prefix}_$timestamp.jpg';
    return await saveImageToAppDirectory(imageFile, fileName);
  }

  Future<Uint8List> getImageBytes(File imageFile) async {
    try {
      return await imageFile.readAsBytes();
    } catch (e) {
      throw CameraException('Failed to read image bytes: $e');
    }
  }

  Future<String> getImageBase64(File imageFile) async {
    try {
      final bytes = await getImageBytes(imageFile);
      return ImageUtils.bytesToBase64(bytes);
    } catch (e) {
      throw CameraException('Failed to convert image to base64: $e');
    }
  }

  Future<void> disposeCamera() async {
    if (_controller != null) {
      await _controller!.dispose();
      _controller = null;
    }
  }

  bool get isCameraInitialized {
    return _controller != null && _controller!.value.isInitialized;
  }

  CameraController? get controller => _controller;

  List<CameraDescription>? get availableCameras => _cameras;

  Future<void> switchCamera() async {
    if (_cameras == null || _cameras!.length < 2) {
      throw CameraException('Cannot switch camera - insufficient cameras available');
    }

    final currentDirection = _controller?.description.lensDirection;
    final newDirection = currentDirection == CameraLensDirection.back
        ? CameraLensDirection.front
        : CameraLensDirection.back;

    await disposeCamera();
    await initializeCamera(direction: newDirection);
  }

  Future<void> setFlashMode(FlashMode mode) async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw CameraException('Camera not initialized');
    }

    try {
      await _controller!.setFlashMode(mode);
    } catch (e) {
      throw CameraException('Failed to set flash mode: $e');
    }
  }

  Future<void> setZoomLevel(double zoom) async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw CameraException('Camera not initialized');
    }

    try {
      final maxZoom = await _controller!.getMaxZoomLevel();
      final minZoom = await _controller!.getMinZoomLevel();
      final clampedZoom = zoom.clamp(minZoom, maxZoom);
      await _controller!.setZoomLevel(clampedZoom);
    } catch (e) {
      throw CameraException('Failed to set zoom level: $e');
    }
  }

  Future<double> getMaxZoomLevel() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw CameraException('Camera not initialized');
    }

    try {
      return await _controller!.getMaxZoomLevel();
    } catch (e) {
      throw CameraException('Failed to get max zoom level: $e');
    }
  }

  Future<double> getMinZoomLevel() async {
    if (_controller == null || !_controller!.value.isInitialized) {
      throw CameraException('Camera not initialized');
    }

    try {
      return await _controller!.getMinZoomLevel();
    } catch (e) {
      throw CameraException('Failed to get min zoom level: $e');
    }
  }
}
