import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../data/models/property_model.dart';

abstract class PropertyRepository {
  Future<Either<Failure, List<PropertyModel>>> getProperties(
    int page,
    int limit,
    String? search,
  );
  
  Future<Either<Failure, PropertyModel>> getPropertyDetails(String propertyId);
  
  Future<Either<Failure, Map<String, dynamic>>> getPropertyStatus(String propertyId);
}
