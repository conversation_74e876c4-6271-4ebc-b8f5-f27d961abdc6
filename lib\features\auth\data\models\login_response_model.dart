import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

import 'user_model.dart';

part 'login_response_model.g.dart';

@JsonSerializable()
class LoginResponseModel extends Equatable {
  final String accessToken;
  final String refreshToken;
  final UserModel user;
  final int expiresIn;
  final String tokenType;

  const LoginResponseModel({
    required this.accessToken,
    required this.refreshToken,
    required this.user,
    required this.expiresIn,
    this.tokenType = 'Bearer',
  });

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) =>
      _$LoginResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResponseModelToJson(this);

  DateTime get expiresAt {
    return DateTime.now().add(Duration(seconds: expiresIn));
  }

  bool get isExpired {
    return DateTime.now().isAfter(expiresAt);
  }

  LoginResponseModel copyWith({
    String? accessToken,
    String? refreshToken,
    UserModel? user,
    int? expiresIn,
    String? tokenType,
  }) {
    return LoginResponseModel(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      user: user ?? this.user,
      expiresIn: expiresIn ?? this.expiresIn,
      tokenType: tokenType ?? this.tokenType,
    );
  }

  @override
  List<Object?> get props => [
        accessToken,
        refreshToken,
        user,
        expiresIn,
        tokenType,
      ];

  @override
  String toString() {
    return 'LoginResponseModel(user: ${user.email}, tokenType: $tokenType, expiresIn: $expiresIn)';
  }
}
