import 'package:equatable/equatable.dart';

enum UserRole {
  admin,
  manager,
  fieldWorker,
  security,
  guest,
}

extension UserRoleExtension on UserRole {
  String get name {
    switch (this) {
      case UserRole.admin:
        return 'admin';
      case UserRole.manager:
        return 'manager';
      case UserRole.fieldWorker:
        return 'field_worker';
      case UserRole.security:
        return 'security';
      case UserRole.guest:
        return 'guest';
    }
  }

  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.manager:
        return 'Manager';
      case UserRole.fieldWorker:
        return 'Field Worker';
      case UserRole.security:
        return 'Security Personnel';
      case UserRole.guest:
        return 'Guest';
    }
  }

  List<String> get defaultPermissions {
    switch (this) {
      case UserRole.admin:
        return [
          'view_all_properties',
          'manage_properties',
          'view_all_fuel',
          'manage_fuel',
          'view_all_maintenance',
          'manage_maintenance',
          'view_all_attendance',
          'manage_attendance',
          'view_all_ott',
          'manage_ott',
          'manage_users',
          'view_analytics',
          'manage_settings',
        ];
      case UserRole.manager:
        return [
          'view_assigned_properties',
          'view_all_fuel',
          'manage_fuel',
          'view_all_maintenance',
          'manage_maintenance',
          'view_all_attendance',
          'view_all_ott',
          'manage_ott',
          'view_analytics',
        ];
      case UserRole.fieldWorker:
        return [
          'view_assigned_properties',
          'view_fuel',
          'update_fuel',
          'view_maintenance',
          'create_maintenance',
          'update_maintenance',
          'record_attendance',
          'view_ott',
        ];
      case UserRole.security:
        return [
          'view_assigned_properties',
          'view_security',
          'manage_security',
          'create_incidents',
          'record_attendance',
        ];
      case UserRole.guest:
        return [
          'view_assigned_properties',
        ];
    }
  }
}

class User extends Equatable {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final UserRole role;
  final String? phone;
  final String? avatar;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> permissions;
  final Map<String, dynamic> metadata;

  const User({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.role,
    this.phone,
    this.avatar,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.permissions,
    required this.metadata,
  });

  String get fullName => '$firstName $lastName';

  String get initials {
    final first = firstName.isNotEmpty ? firstName[0] : '';
    final last = lastName.isNotEmpty ? lastName[0] : '';
    return '$first$last'.toUpperCase();
  }

  bool hasPermission(String permission) {
    return permissions.contains(permission);
  }

  bool get isAdmin => role == UserRole.admin;
  bool get isManager => role == UserRole.manager;
  bool get isFieldWorker => role == UserRole.fieldWorker;
  bool get isSecurity => role == UserRole.security;
  bool get isGuest => role == UserRole.guest;

  bool canViewProperty(String propertyId) {
    if (isAdmin || isManager) return true;
    
    // Check if user has access to specific property
    final assignedProperties = metadata['assignedProperties'] as List<String>?;
    return assignedProperties?.contains(propertyId) ?? false;
  }

  bool canManageProperty(String propertyId) {
    if (isAdmin) return true;
    if (isManager && canViewProperty(propertyId)) return true;
    return false;
  }

  bool canUpdateFuel(String propertyId) {
    if (isAdmin || isManager) return true;
    if (isFieldWorker && canViewProperty(propertyId)) {
      return hasPermission('update_fuel');
    }
    return false;
  }

  bool canCreateMaintenance(String propertyId) {
    if (isAdmin || isManager) return true;
    if (isFieldWorker && canViewProperty(propertyId)) {
      return hasPermission('create_maintenance');
    }
    return false;
  }

  bool canRecordAttendance() {
    return hasPermission('record_attendance');
  }

  bool canViewAnalytics() {
    return hasPermission('view_analytics');
  }

  bool canManageUsers() {
    return hasPermission('manage_users');
  }

  User copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    UserRole? role,
    String? phone,
    String? avatar,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? permissions,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      role: role ?? this.role,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      permissions: permissions ?? this.permissions,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        firstName,
        lastName,
        role,
        phone,
        avatar,
        isActive,
        createdAt,
        updatedAt,
        permissions,
        metadata,
      ];

  @override
  String toString() {
    return 'User(id: $id, email: $email, fullName: $fullName, role: ${role.displayName})';
  }
}
