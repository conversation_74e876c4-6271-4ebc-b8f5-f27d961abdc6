import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../network/api_client.dart';
import '../network/dio_client.dart';
import '../services/storage_service.dart';
import '../services/location_service.dart';
import '../services/camera_service.dart';
import '../services/notification_service.dart';
import '../services/connectivity_service.dart';
import '../services/sync_service.dart';

import '../../features/auth/data/datasources/auth_remote_datasource.dart';
import '../../features/auth/data/datasources/auth_local_datasource.dart';
import '../../features/auth/data/repositories/auth_repository_impl.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../../features/auth/domain/usecases/login_usecase.dart';
import '../../features/auth/domain/usecases/logout_usecase.dart';
import '../../features/auth/domain/usecases/check_auth_status_usecase.dart';
import '../../features/auth/presentation/bloc/auth_bloc.dart';

import '../../features/dashboard/data/datasources/dashboard_remote_datasource.dart';
import '../../features/dashboard/data/repositories/dashboard_repository_impl.dart';
import '../../features/dashboard/domain/repositories/dashboard_repository.dart';
import '../../features/dashboard/domain/usecases/get_dashboard_data_usecase.dart';
import '../../features/dashboard/presentation/bloc/dashboard_bloc.dart';

import '../../features/property/data/datasources/property_remote_datasource.dart';
import '../../features/property/data/datasources/property_local_datasource.dart';
import '../../features/property/data/repositories/property_repository_impl.dart';
import '../../features/property/domain/repositories/property_repository.dart';
import '../../features/property/domain/usecases/get_properties_usecase.dart';
import '../../features/property/domain/usecases/get_property_details_usecase.dart';
import '../../features/property/presentation/bloc/property_bloc.dart';

import '../../features/fuel/data/datasources/fuel_remote_datasource.dart';
import '../../features/fuel/data/datasources/fuel_local_datasource.dart';
import '../../features/fuel/data/repositories/fuel_repository_impl.dart';
import '../../features/fuel/domain/repositories/fuel_repository.dart';
import '../../features/fuel/domain/usecases/get_fuel_status_usecase.dart';
import '../../features/fuel/domain/usecases/update_fuel_level_usecase.dart';
import '../../features/fuel/presentation/bloc/fuel_bloc.dart';

final GetIt getIt = GetIt.instance;

Future<void> initializeDependencies() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerLazySingleton(() => sharedPreferences);
  
  const secureStorage = FlutterSecureStorage();
  getIt.registerLazySingleton(() => secureStorage);
  
  // Core services
  getIt.registerLazySingleton<Dio>(() => DioClient.createDio());
  getIt.registerLazySingleton<ApiClient>(() => ApiClient(getIt<Dio>()));
  getIt.registerLazySingleton<StorageService>(() => StorageService());
  getIt.registerLazySingleton<LocationService>(() => LocationService());
  getIt.registerLazySingleton<CameraService>(() => CameraService());
  getIt.registerLazySingleton<NotificationService>(() => NotificationService());
  getIt.registerLazySingleton<ConnectivityService>(() => ConnectivityService());
  getIt.registerLazySingleton<SyncService>(() => SyncService());
  
  // Auth feature
  getIt.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(getIt<StorageService>()),
  );
  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      remoteDataSource: getIt<AuthRemoteDataSource>(),
      localDataSource: getIt<AuthLocalDataSource>(),
    ),
  );
  getIt.registerLazySingleton(() => LoginUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton(() => LogoutUseCase(getIt<AuthRepository>()));
  getIt.registerLazySingleton(() => CheckAuthStatusUseCase(getIt<AuthRepository>()));
  getIt.registerFactory(() => AuthBloc(
    loginUseCase: getIt<LoginUseCase>(),
    logoutUseCase: getIt<LogoutUseCase>(),
    checkAuthStatusUseCase: getIt<CheckAuthStatusUseCase>(),
  ));
  
  // Dashboard feature
  getIt.registerLazySingleton<DashboardRemoteDataSource>(
    () => DashboardRemoteDataSourceImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<DashboardRepository>(
    () => DashboardRepositoryImpl(getIt<DashboardRemoteDataSource>()),
  );
  getIt.registerLazySingleton(() => GetDashboardDataUseCase(getIt<DashboardRepository>()));
  getIt.registerFactory(() => DashboardBloc(getIt<GetDashboardDataUseCase>()));
  
  // Property feature
  getIt.registerLazySingleton<PropertyRemoteDataSource>(
    () => PropertyRemoteDataSourceImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<PropertyLocalDataSource>(
    () => PropertyLocalDataSourceImpl(getIt<StorageService>()),
  );
  getIt.registerLazySingleton<PropertyRepository>(
    () => PropertyRepositoryImpl(
      remoteDataSource: getIt<PropertyRemoteDataSource>(),
      localDataSource: getIt<PropertyLocalDataSource>(),
    ),
  );
  getIt.registerLazySingleton(() => GetPropertiesUseCase(getIt<PropertyRepository>()));
  getIt.registerLazySingleton(() => GetPropertyDetailsUseCase(getIt<PropertyRepository>()));
  getIt.registerFactory(() => PropertyBloc(
    getPropertiesUseCase: getIt<GetPropertiesUseCase>(),
    getPropertyDetailsUseCase: getIt<GetPropertyDetailsUseCase>(),
  ));
  
  // Fuel feature
  getIt.registerLazySingleton<FuelRemoteDataSource>(
    () => FuelRemoteDataSourceImpl(getIt<ApiClient>()),
  );
  getIt.registerLazySingleton<FuelLocalDataSource>(
    () => FuelLocalDataSourceImpl(getIt<StorageService>()),
  );
  getIt.registerLazySingleton<FuelRepository>(
    () => FuelRepositoryImpl(
      remoteDataSource: getIt<FuelRemoteDataSource>(),
      localDataSource: getIt<FuelLocalDataSource>(),
    ),
  );
  getIt.registerLazySingleton(() => GetFuelStatusUseCase(getIt<FuelRepository>()));
  getIt.registerLazySingleton(() => UpdateFuelLevelUseCase(getIt<FuelRepository>()));
  getIt.registerFactory(() => FuelBloc(
    getFuelStatusUseCase: getIt<GetFuelStatusUseCase>(),
    updateFuelLevelUseCase: getIt<UpdateFuelLevelUseCase>(),
  ));
}
