import 'package:equatable/equatable.dart';

import '../../domain/entities/user.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {
  final User user;
  final bool isBiometricEnabled;

  const AuthAuthenticated({
    required this.user,
    this.isBiometricEnabled = false,
  });

  @override
  List<Object?> get props => [user, isBiometricEnabled];

  AuthAuthenticated copyWith({
    User? user,
    bool? isBiometricEnabled,
  }) {
    return AuthAuthenticated(
      user: user ?? this.user,
      isBiometricEnabled: isBiometricEnabled ?? this.isBiometricEnabled,
    );
  }
}

class AuthUnauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;
  final String? errorCode;

  const AuthError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class AuthPasswordChangeSuccess extends AuthState {}

class AuthForgotPasswordSuccess extends AuthState {
  final String message;

  const AuthForgotPasswordSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class AuthResetPasswordSuccess extends AuthState {
  final String message;

  const AuthResetPasswordSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class AuthProfileUpdateSuccess extends AuthState {
  final User user;

  const AuthProfileUpdateSuccess({required this.user});

  @override
  List<Object?> get props => [user];
}

class AuthBiometricToggleSuccess extends AuthState {
  final bool isEnabled;

  const AuthBiometricToggleSuccess({required this.isEnabled});

  @override
  List<Object?> get props => [isEnabled];
}

class AuthAccountDeleteSuccess extends AuthState {}
