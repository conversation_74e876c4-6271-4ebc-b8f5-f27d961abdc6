import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/fuel_repository.dart';
import '../../data/models/fuel_status_model.dart';

class GetFuelStatusUseCase implements UseCase<FuelStatusModel, GetFuelStatusParams> {
  final FuelRepository repository;

  GetFuelStatusUseCase(this.repository);

  @override
  Future<Either<Failure, FuelStatusModel>> call(GetFuelStatusParams params) async {
    return await repository.getFuelStatus(params.propertyId);
  }
}

class GetFuelStatusParams extends Equatable {
  final String propertyId;

  const GetFuelStatusParams({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}
