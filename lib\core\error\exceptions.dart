import 'package:equatable/equatable.dart';

abstract class AppException extends Equatable implements Exception {
  final String message;
  final int? statusCode;

  const AppException({
    required this.message,
    this.statusCode,
  });

  @override
  List<Object?> get props => [message, statusCode];

  @override
  String toString() => message;
}

class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.statusCode,
  });
}

class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.statusCode,
  });
}

class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.statusCode,
  });
}

class AuthException extends AppException {
  const AuthException({
    required super.message,
    super.statusCode,
  });
}

class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.statusCode,
  });
}

class LocationException extends AppException {
  const LocationException(String message) : super(message: message);
}

class <PERSON>Exception extends AppException {
  const CameraException(String message) : super(message: message);
}

class StorageException extends AppException {
  const StorageException({
    required super.message,
    super.statusCode,
  });
}

class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.statusCode,
  });
}

class SyncException extends AppException {
  const SyncException({
    required super.message,
    super.statusCode,
  });
}

class NotificationException extends AppException {
  const NotificationException({
    required super.message,
    super.statusCode,
  });
}

class QRCodeException extends AppException {
  const QRCodeException({
    required super.message,
    super.statusCode,
  });
}
