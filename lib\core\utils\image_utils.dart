import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

class ImageUtils {
  static Future<File> compressImage(
    File imageFile, {
    int maxWidth = 1024,
    int maxHeight = 1024,
    int quality = 85,
  }) async {
    try {
      // For now, return the original file
      // In a real implementation, you would use image compression libraries
      // like flutter_image_compress or image package
      return imageFile;
    } catch (e) {
      throw Exception('Failed to compress image: $e');
    }
  }

  static String bytesToBase64(Uint8List bytes) {
    return base64Encode(bytes);
  }

  static Uint8List base64ToBytes(String base64String) {
    return base64Decode(base64String);
  }

  static Future<String> fileToBase64(File file) async {
    final bytes = await file.readAsBytes();
    return bytesToBase64(bytes);
  }

  static Future<File> base64ToFile(String base64String, String fileName) async {
    final bytes = base64ToBytes(base64String);
    final directory = await getTemporaryDirectory();
    final file = File('${directory.path}/$fileName');
    await file.writeAsBytes(bytes);
    return file;
  }

  static String getFileExtension(String filePath) {
    return filePath.split('.').last.toLowerCase();
  }

  static bool isValidImageExtension(String extension) {
    const validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return validExtensions.contains(extension.toLowerCase());
  }

  static bool isValidImageFile(String filePath) {
    final extension = getFileExtension(filePath);
    return isValidImageExtension(extension);
  }

  static Future<int> getFileSize(File file) async {
    return await file.length();
  }

  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  static Future<File> copyFileToAppDirectory(File sourceFile, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final imagesDir = Directory('${directory.path}/images');
    
    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }

    final targetFile = File('${imagesDir.path}/$fileName');
    return await sourceFile.copy(targetFile.path);
  }

  static Future<void> deleteFile(File file) async {
    if (await file.exists()) {
      await file.delete();
    }
  }

  static Future<List<File>> getImagesFromDirectory(Directory directory) async {
    if (!await directory.exists()) {
      return [];
    }

    final files = await directory.list().toList();
    return files
        .whereType<File>()
        .where((file) => isValidImageFile(file.path))
        .toList();
  }

  static Future<void> clearImageCache() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imagesDir = Directory('${directory.path}/images');
      
      if (await imagesDir.exists()) {
        await imagesDir.delete(recursive: true);
      }
    } catch (e) {
      // Ignore errors when clearing cache
    }
  }

  static String generateUniqueFileName(String prefix, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${prefix}_$timestamp.$extension';
  }

  static Future<File> resizeImage(
    File imageFile, {
    int? width,
    int? height,
    bool maintainAspectRatio = true,
  }) async {
    // For now, return the original file
    // In a real implementation, you would use image processing libraries
    return imageFile;
  }

  static Future<File> rotateImage(File imageFile, int degrees) async {
    // For now, return the original file
    // In a real implementation, you would use image processing libraries
    return imageFile;
  }

  static Future<File> cropImage(
    File imageFile, {
    required int x,
    required int y,
    required int width,
    required int height,
  }) async {
    // For now, return the original file
    // In a real implementation, you would use image processing libraries
    return imageFile;
  }

  static Future<Map<String, dynamic>> getImageMetadata(File imageFile) async {
    final stat = await imageFile.stat();
    final size = await getFileSize(imageFile);
    
    return {
      'path': imageFile.path,
      'name': imageFile.path.split('/').last,
      'size': size,
      'sizeFormatted': formatFileSize(size),
      'extension': getFileExtension(imageFile.path),
      'created': stat.changed.toIso8601String(),
      'modified': stat.modified.toIso8601String(),
    };
  }

  static Future<bool> validateImageFile(File imageFile) async {
    try {
      // Check if file exists
      if (!await imageFile.exists()) {
        return false;
      }

      // Check file extension
      if (!isValidImageFile(imageFile.path)) {
        return false;
      }

      // Check file size (max 10MB)
      final size = await getFileSize(imageFile);
      if (size > 10 * 1024 * 1024) {
        return false;
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<File> addWatermark(
    File imageFile,
    String watermarkText, {
    double opacity = 0.5,
    String position = 'bottom-right',
  }) async {
    // For now, return the original file
    // In a real implementation, you would add watermark functionality
    return imageFile;
  }
}
