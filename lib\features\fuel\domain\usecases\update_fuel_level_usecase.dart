import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/fuel_repository.dart';
import '../../data/models/fuel_status_model.dart';
import '../../data/models/fuel_update_request_model.dart';

class UpdateFuelLevelUseCase implements UseCase<FuelStatusModel, FuelUpdateRequestModel> {
  final FuelRepository repository;

  UpdateFuelLevelUseCase(this.repository);

  @override
  Future<Either<Failure, FuelStatusModel>> call(FuelUpdateRequestModel params) async {
    return await repository.updateFuelLevel(params);
  }
}
