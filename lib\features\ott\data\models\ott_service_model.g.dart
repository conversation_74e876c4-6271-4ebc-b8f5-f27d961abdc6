// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ott_service_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OttServiceModel _$OttServiceModelFromJson(Map<String, dynamic> json) =>
    OttServiceModel(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      serviceName: json['serviceName'] as String,
      provider: json['provider'] as String,
      status: json['status'] as String,
      ipAddress: json['ipAddress'] as String?,
      port: json['port'] as int?,
      username: json['username'] as String?,
      lastChecked: json['lastChecked'] == null
          ? null
          : DateTime.parse(json['lastChecked'] as String),
      isActive: json['isActive'] as bool,
      configuration: json['configuration'] as Map<String, dynamic>?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$OttServiceModelToJson(OttServiceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'serviceName': instance.serviceName,
      'provider': instance.provider,
      'status': instance.status,
      'ipAddress': instance.ipAddress,
      'port': instance.port,
      'username': instance.username,
      'lastChecked': instance.lastChecked?.toIso8601String(),
      'isActive': instance.isActive,
      'configuration': instance.configuration,
      'metadata': instance.metadata,
    };
