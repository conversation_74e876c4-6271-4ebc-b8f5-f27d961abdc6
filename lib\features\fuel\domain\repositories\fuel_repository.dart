import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../data/models/fuel_status_model.dart';
import '../../data/models/fuel_update_request_model.dart';

abstract class FuelRepository {
  Future<Either<Failure, FuelStatusModel>> getFuelStatus(String propertyId);
  
  Future<Either<Failure, FuelStatusModel>> updateFuelLevel(
    FuelUpdateRequestModel request,
  );
  
  Future<Either<Failure, List<FuelStatusModel>>> getFuelHistory(
    String propertyId,
    int page,
    int limit,
  );
  
  Future<Either<Failure, List<FuelUpdateRequestModel>>> getPendingUpdates();
  
  Future<Either<Failure, void>> syncPendingUpdates();
}
