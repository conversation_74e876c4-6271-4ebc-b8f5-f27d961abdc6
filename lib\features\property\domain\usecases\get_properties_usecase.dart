import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/property_repository.dart';
import '../../data/models/property_model.dart';

class GetPropertiesUseCase implements UseCase<List<PropertyModel>, GetPropertiesParams> {
  final PropertyRepository repository;

  GetPropertiesUseCase(this.repository);

  @override
  Future<Either<Failure, List<PropertyModel>>> call(GetPropertiesParams params) async {
    return await repository.getProperties(
      params.page,
      params.limit,
      params.search,
    );
  }
}

class GetPropertiesParams extends Equatable {
  final int page;
  final int limit;
  final String? search;

  const GetPropertiesParams({
    required this.page,
    required this.limit,
    this.search,
  });

  @override
  List<Object?> get props => [page, limit, search];
}
