import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'fuel_status_model.g.dart';

@JsonSerializable()
class FuelStatusModel extends Equatable {
  final String id;
  final String propertyId;
  final double currentLevel;
  final double capacity;
  final String unit;
  final DateTime lastUpdated;
  final String? lastUpdatedBy;
  final double? consumptionRate;
  final DateTime? estimatedEmptyDate;
  final String status;
  final List<String>? images;
  final String? notes;
  final Map<String, dynamic>? metadata;

  const FuelStatusModel({
    required this.id,
    required this.propertyId,
    required this.currentLevel,
    required this.capacity,
    required this.unit,
    required this.lastUpdated,
    this.lastUpdatedBy,
    this.consumptionRate,
    this.estimatedEmptyDate,
    required this.status,
    this.images,
    this.notes,
    this.metadata,
  });

  factory FuelStatusModel.fromJson(Map<String, dynamic> json) =>
      _$FuelStatusModelFromJson(json);

  Map<String, dynamic> toJson() => _$FuelStatusModelToJson(this);

  double get percentageFull => (currentLevel / capacity) * 100;

  bool get isLow => percentageFull < 25;
  bool get isCritical => percentageFull < 10;

  @override
  List<Object?> get props => [
        id,
        propertyId,
        currentLevel,
        capacity,
        unit,
        lastUpdated,
        lastUpdatedBy,
        consumptionRate,
        estimatedEmptyDate,
        status,
        images,
        notes,
        metadata,
      ];
}
