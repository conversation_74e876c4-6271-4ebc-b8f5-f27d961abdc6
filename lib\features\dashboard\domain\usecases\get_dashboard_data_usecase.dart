import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/dashboard_repository.dart';
import '../../data/models/dashboard_data_model.dart';

class GetDashboardDataUseCase implements UseCaseNoParams<DashboardDataModel> {
  final DashboardRepository repository;

  GetDashboardDataUseCase(this.repository);

  @override
  Future<Either<Failure, DashboardDataModel>> call() async {
    return await repository.getDashboardData();
  }
}
