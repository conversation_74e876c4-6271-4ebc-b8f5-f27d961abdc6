import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../../features/auth/data/models/login_request_model.dart';
import '../../features/auth/data/models/login_response_model.dart';
import '../../features/auth/data/models/user_model.dart';
import '../../features/dashboard/data/models/dashboard_data_model.dart';
import '../../features/property/data/models/property_model.dart';
import '../../features/fuel/data/models/fuel_status_model.dart';
import '../../features/fuel/data/models/fuel_update_request_model.dart';
import '../../features/maintenance/data/models/maintenance_issue_model.dart';
import '../../features/attendance/data/models/attendance_model.dart';
import '../../features/ott/data/models/ott_service_model.dart';

part 'api_client.g.dart';

@RestApi()
abstract class ApiClient {
  factory ApiClient(Dio dio, {String baseUrl}) = _ApiClient;

  // Authentication endpoints
  @POST('/auth/login')
  Future<LoginResponseModel> login(@Body() LoginRequestModel request);

  @POST('/auth/register')
  Future<UserModel> register(@Body() Map<String, dynamic> request);

  @POST('/auth/refresh')
  Future<LoginResponseModel> refreshToken(@Body() Map<String, dynamic> request);

  @POST('/auth/logout')
  Future<void> logout();

  @GET('/auth/me')
  Future<UserModel> getCurrentUser();

  // Dashboard endpoints
  @GET('/dashboard/status')
  Future<DashboardDataModel> getDashboardData();

  @GET('/dashboard/analytics')
  Future<Map<String, dynamic>> getDashboardAnalytics();

  // Property endpoints
  @GET('/properties')
  Future<List<PropertyModel>> getProperties(
    @Query('page') int page,
    @Query('limit') int limit,
    @Query('search') String? search,
  );

  @GET('/properties/{id}')
  Future<PropertyModel> getPropertyDetails(@Path('id') String propertyId);

  @GET('/properties/{id}/status')
  Future<Map<String, dynamic>> getPropertyStatus(@Path('id') String propertyId);

  // Generator Fuel endpoints
  @GET('/generator-fuel/{propertyId}')
  Future<FuelStatusModel> getFuelStatus(@Path('propertyId') String propertyId);

  @POST('/generator-fuel')
  Future<FuelStatusModel> updateFuelLevel(@Body() FuelUpdateRequestModel request);

  @PUT('/generator-fuel/{id}')
  Future<FuelStatusModel> updateFuelRecord(
    @Path('id') String id,
    @Body() Map<String, dynamic> request,
  );

  @GET('/generator-fuel/{propertyId}/history')
  Future<List<FuelStatusModel>> getFuelHistory(
    @Path('propertyId') String propertyId,
    @Query('page') int page,
    @Query('limit') int limit,
  );

  // Maintenance endpoints
  @GET('/maintenance-issues')
  Future<List<MaintenanceIssueModel>> getMaintenanceIssues(
    @Query('propertyId') String? propertyId,
    @Query('status') String? status,
    @Query('page') int page,
    @Query('limit') int limit,
  );

  @POST('/maintenance-issues')
  Future<MaintenanceIssueModel> createMaintenanceIssue(
    @Body() Map<String, dynamic> request,
  );

  @PUT('/maintenance-issues/{id}')
  Future<MaintenanceIssueModel> updateMaintenanceIssue(
    @Path('id') String id,
    @Body() Map<String, dynamic> request,
  );

  @GET('/maintenance-issues/{id}')
  Future<MaintenanceIssueModel> getMaintenanceIssueDetails(@Path('id') String id);

  // Attendance endpoints
  @GET('/attendance/{siteId}')
  Future<List<AttendanceModel>> getAttendance(
    @Path('siteId') String siteId,
    @Query('date') String? date,
  );

  @POST('/attendance')
  Future<AttendanceModel> recordAttendance(@Body() Map<String, dynamic> request);

  @GET('/attendance/reports')
  Future<Map<String, dynamic>> getAttendanceReports(
    @Query('startDate') String startDate,
    @Query('endDate') String endDate,
    @Query('siteId') String? siteId,
  );

  // OTT Services endpoints
  @GET('/ott-services/{propertyId}')
  Future<List<OttServiceModel>> getOttServices(@Path('propertyId') String propertyId);

  @POST('/ott-services')
  Future<OttServiceModel> createOttService(@Body() Map<String, dynamic> request);

  @PUT('/ott-services/{id}')
  Future<OttServiceModel> updateOttService(
    @Path('id') String id,
    @Body() Map<String, dynamic> request,
  );

  // File upload endpoints
  @POST('/upload/image')
  @MultiPart()
  Future<Map<String, dynamic>> uploadImage(@Part() MultipartFile file);

  @POST('/upload/document')
  @MultiPart()
  Future<Map<String, dynamic>> uploadDocument(@Part() MultipartFile file);

  // Admin endpoints
  @GET('/admin/users')
  Future<List<UserModel>> getUsers(
    @Query('page') int page,
    @Query('limit') int limit,
    @Query('role') String? role,
  );

  @POST('/admin/users')
  Future<UserModel> createUser(@Body() Map<String, dynamic> request);

  @PUT('/admin/users/{id}')
  Future<UserModel> updateUser(
    @Path('id') String id,
    @Body() Map<String, dynamic> request,
  );

  @GET('/admin/thresholds')
  Future<Map<String, dynamic>> getThresholds();

  @PUT('/admin/thresholds')
  Future<Map<String, dynamic>> updateThresholds(@Body() Map<String, dynamic> request);
}
