import '../../../../core/network/api_client.dart';
import '../models/login_request_model.dart';
import '../models/login_response_model.dart';
import '../models/user_model.dart';

abstract class AuthRemoteDataSource {
  Future<LoginResponseModel> login(LoginRequestModel request);
  Future<UserModel> getCurrentUser();
  Future<LoginResponseModel> refreshToken(String refreshToken);
  Future<void> logout();
  Future<void> updateFCMToken(String token);
  Future<UserModel> updateProfile(Map<String, dynamic> data);
  Future<void> changePassword(String currentPassword, String newPassword);
  Future<void> forgotPassword(String email);
  Future<void> resetPassword(String token, String newPassword);
  Future<void> deleteAccount();
}

class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final ApiClient apiClient;

  AuthRemoteDataSourceImpl(this.apiClient);

  @override
  Future<LoginResponseModel> login(LoginRequestModel request) async {
    try {
      return await apiClient.login(request);
    } catch (e) {
      throw Exception('Login failed: $e');
    }
  }

  @override
  Future<UserModel> getCurrentUser() async {
    try {
      return await apiClient.getCurrentUser();
    } catch (e) {
      throw Exception('Failed to get current user: $e');
    }
  }

  @override
  Future<LoginResponseModel> refreshToken(String refreshToken) async {
    try {
      return await apiClient.refreshToken({'refreshToken': refreshToken});
    } catch (e) {
      throw Exception('Token refresh failed: $e');
    }
  }

  @override
  Future<void> logout() async {
    try {
      await apiClient.logout();
    } catch (e) {
      throw Exception('Logout failed: $e');
    }
  }

  @override
  Future<void> updateFCMToken(String token) async {
    try {
      // TODO: Implement FCM token update API call
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      throw Exception('FCM token update failed: $e');
    }
  }

  @override
  Future<UserModel> updateProfile(Map<String, dynamic> data) async {
    try {
      // TODO: Implement profile update API call
      await Future.delayed(const Duration(milliseconds: 500));
      throw UnimplementedError('Profile update not implemented');
    } catch (e) {
      throw Exception('Profile update failed: $e');
    }
  }

  @override
  Future<void> changePassword(String currentPassword, String newPassword) async {
    try {
      // TODO: Implement password change API call
      await Future.delayed(const Duration(milliseconds: 500));
      throw UnimplementedError('Password change not implemented');
    } catch (e) {
      throw Exception('Password change failed: $e');
    }
  }

  @override
  Future<void> forgotPassword(String email) async {
    try {
      // TODO: Implement forgot password API call
      await Future.delayed(const Duration(milliseconds: 500));
      throw UnimplementedError('Forgot password not implemented');
    } catch (e) {
      throw Exception('Forgot password failed: $e');
    }
  }

  @override
  Future<void> resetPassword(String token, String newPassword) async {
    try {
      // TODO: Implement reset password API call
      await Future.delayed(const Duration(milliseconds: 500));
      throw UnimplementedError('Reset password not implemented');
    } catch (e) {
      throw Exception('Reset password failed: $e');
    }
  }

  @override
  Future<void> deleteAccount() async {
    try {
      // TODO: Implement account deletion API call
      await Future.delayed(const Duration(milliseconds: 500));
      throw UnimplementedError('Account deletion not implemented');
    } catch (e) {
      throw Exception('Account deletion failed: $e');
    }
  }
}
