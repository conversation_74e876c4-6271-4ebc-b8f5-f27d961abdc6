import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../domain/usecases/get_fuel_status_usecase.dart';
import '../../domain/usecases/update_fuel_level_usecase.dart';
import '../../data/models/fuel_status_model.dart';
import '../../data/models/fuel_update_request_model.dart';

// Events
abstract class FuelEvent extends Equatable {
  const FuelEvent();

  @override
  List<Object?> get props => [];
}

class FuelStatusRequested extends FuelEvent {
  final String propertyId;

  const FuelStatusRequested({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

class FuelLevelUpdateRequested extends FuelEvent {
  final FuelUpdateRequestModel request;

  const FuelLevelUpdateRequested({required this.request});

  @override
  List<Object?> get props => [request];
}

class FuelRefreshRequested extends FuelEvent {
  final String propertyId;

  const FuelRefreshRequested({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

// States
abstract class FuelState extends Equatable {
  const FuelState();

  @override
  List<Object?> get props => [];
}

class FuelInitial extends FuelState {}

class FuelLoading extends FuelState {}

class FuelStatusLoaded extends FuelState {
  final FuelStatusModel fuelStatus;

  const FuelStatusLoaded({required this.fuelStatus});

  @override
  List<Object?> get props => [fuelStatus];
}

class FuelUpdateSuccess extends FuelState {
  final FuelStatusModel updatedStatus;

  const FuelUpdateSuccess({required this.updatedStatus});

  @override
  List<Object?> get props => [updatedStatus];
}

class FuelError extends FuelState {
  final String message;

  const FuelError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class FuelBloc extends Bloc<FuelEvent, FuelState> {
  final GetFuelStatusUseCase getFuelStatusUseCase;
  final UpdateFuelLevelUseCase updateFuelLevelUseCase;

  FuelBloc({
    required this.getFuelStatusUseCase,
    required this.updateFuelLevelUseCase,
  }) : super(FuelInitial()) {
    on<FuelStatusRequested>(_onFuelStatusRequested);
    on<FuelLevelUpdateRequested>(_onFuelLevelUpdateRequested);
    on<FuelRefreshRequested>(_onFuelRefreshRequested);
  }

  Future<void> _onFuelStatusRequested(
    FuelStatusRequested event,
    Emitter<FuelState> emit,
  ) async {
    emit(FuelLoading());

    final result = await getFuelStatusUseCase(
      GetFuelStatusParams(propertyId: event.propertyId),
    );

    result.fold(
      (failure) => emit(FuelError(message: failure.message)),
      (fuelStatus) => emit(FuelStatusLoaded(fuelStatus: fuelStatus)),
    );
  }

  Future<void> _onFuelLevelUpdateRequested(
    FuelLevelUpdateRequested event,
    Emitter<FuelState> emit,
  ) async {
    emit(FuelLoading());

    final result = await updateFuelLevelUseCase(event.request);

    result.fold(
      (failure) => emit(FuelError(message: failure.message)),
      (updatedStatus) => emit(FuelUpdateSuccess(updatedStatus: updatedStatus)),
    );
  }

  Future<void> _onFuelRefreshRequested(
    FuelRefreshRequested event,
    Emitter<FuelState> emit,
  ) async {
    // Don't show loading for refresh
    final result = await getFuelStatusUseCase(
      GetFuelStatusParams(propertyId: event.propertyId),
    );

    result.fold(
      (failure) => emit(FuelError(message: failure.message)),
      (fuelStatus) => emit(FuelStatusLoaded(fuelStatus: fuelStatus)),
    );
  }
}
