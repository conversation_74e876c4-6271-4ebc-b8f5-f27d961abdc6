import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

import '../config/app_config.dart';
import '../error/exceptions.dart';

class LocationService {
  static const LocationSettings _locationSettings = LocationSettings(
    accuracy: LocationAccuracy.high,
    distanceFilter: 10,
    timeLimit: Duration(seconds: AppConfig.locationTimeoutSeconds),
  );

  Future<bool> requestLocationPermission() async {
    final permission = await Permission.location.request();
    return permission.isGranted;
  }

  Future<bool> isLocationPermissionGranted() async {
    final permission = await Permission.location.status;
    return permission.isGranted;
  }

  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  Future<Position> getCurrentLocation() async {
    // Check if location services are enabled
    if (!await isLocationServiceEnabled()) {
      throw LocationException('Location services are disabled');
    }

    // Check location permission
    if (!await isLocationPermissionGranted()) {
      final granted = await requestLocationPermission();
      if (!granted) {
        throw LocationException('Location permission denied');
      }
    }

    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: _locationSettings,
      );
      return position;
    } catch (e) {
      throw LocationException('Failed to get current location: $e');
    }
  }

  Future<Position?> getLastKnownLocation() async {
    try {
      return await Geolocator.getLastKnownPosition();
    } catch (e) {
      return null;
    }
  }

  Stream<Position> getLocationStream() {
    return Geolocator.getPositionStream(locationSettings: _locationSettings);
  }

  double calculateDistance(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  double calculateBearing(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.bearingBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  bool isWithinRadius(
    double centerLatitude,
    double centerLongitude,
    double targetLatitude,
    double targetLongitude,
    double radiusInMeters,
  ) {
    final distance = calculateDistance(
      centerLatitude,
      centerLongitude,
      targetLatitude,
      targetLongitude,
    );
    return distance <= radiusInMeters;
  }

  Future<bool> isAtLocation(
    double targetLatitude,
    double targetLongitude, {
    double accuracyInMeters = AppConfig.locationAccuracy,
  }) async {
    try {
      final currentPosition = await getCurrentLocation();
      return isWithinRadius(
        targetLatitude,
        targetLongitude,
        currentPosition.latitude,
        currentPosition.longitude,
        accuracyInMeters,
      );
    } catch (e) {
      return false;
    }
  }

  Future<LocationPermission> checkLocationPermission() async {
    return await Geolocator.checkPermission();
  }

  Future<LocationPermission> requestPermission() async {
    return await Geolocator.requestPermission();
  }

  Future<void> openLocationSettings() async {
    await Geolocator.openLocationSettings();
  }

  Future<void> openAppSettings() async {
    await Geolocator.openAppSettings();
  }

  String formatCoordinates(double latitude, double longitude) {
    return '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';
  }

  String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.toStringAsFixed(0)} m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(2)} km';
    }
  }

  Map<String, double> parseCoordinates(String coordinatesString) {
    final parts = coordinatesString.split(',');
    if (parts.length != 2) {
      throw const FormatException('Invalid coordinates format');
    }
    
    return {
      'latitude': double.parse(parts[0].trim()),
      'longitude': double.parse(parts[1].trim()),
    };
  }

  Future<bool> validateLocationAccuracy(Position position) async {
    // Check if the location accuracy is acceptable
    return position.accuracy <= AppConfig.locationAccuracy * 2;
  }

  Future<Position> waitForAccurateLocation({
    int maxAttempts = 3,
    Duration delayBetweenAttempts = const Duration(seconds: 2),
  }) async {
    for (int i = 0; i < maxAttempts; i++) {
      try {
        final position = await getCurrentLocation();
        if (await validateLocationAccuracy(position)) {
          return position;
        }
        
        if (i < maxAttempts - 1) {
          await Future.delayed(delayBetweenAttempts);
        }
      } catch (e) {
        if (i == maxAttempts - 1) rethrow;
        await Future.delayed(delayBetweenAttempts);
      }
    }
    
    throw LocationException('Could not get accurate location after $maxAttempts attempts');
  }

  Future<Map<String, dynamic>> getLocationInfo() async {
    final position = await getCurrentLocation();
    final lastKnown = await getLastKnownLocation();
    final permission = await checkLocationPermission();
    final serviceEnabled = await isLocationServiceEnabled();
    
    return {
      'current': {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'altitude': position.altitude,
        'heading': position.heading,
        'speed': position.speed,
        'timestamp': position.timestamp?.toIso8601String(),
      },
      'lastKnown': lastKnown != null ? {
        'latitude': lastKnown.latitude,
        'longitude': lastKnown.longitude,
        'timestamp': lastKnown.timestamp?.toIso8601String(),
      } : null,
      'permission': permission.toString(),
      'serviceEnabled': serviceEnabled,
    };
  }
}
