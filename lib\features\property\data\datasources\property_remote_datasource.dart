import '../models/property_model.dart';

abstract class PropertyRemoteDataSource {
  Future<List<PropertyModel>> getProperties(int page, int limit, String? search);
  Future<PropertyModel> getPropertyDetails(String propertyId);
  Future<Map<String, dynamic>> getPropertyStatus(String propertyId);
}

class PropertyRemoteDataSourceImpl implements PropertyRemoteDataSource {
  PropertyRemoteDataSourceImpl();

  @override
  Future<List<PropertyModel>> getProperties(int page, int limit, String? search) async {
    try {
      // Mock properties data for demo purposes
      await Future.delayed(const Duration(seconds: 1));

      final mockProperties = [
        PropertyModel(
          id: 'prop1',
          name: 'Downtown Office Complex',
          address: '123 Main St, Downtown',
          type: 'Commercial',
          status: 'Active',
          latitude: 40.7128,
          longitude: -74.0060,
          description: 'Modern office complex with 24/7 power backup',
          imageUrl: 'https://example.com/property1.jpg',
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now(),
        ),
        PropertyModel(
          id: 'prop2',
          name: 'Residential Tower A',
          address: '456 Oak Ave, Uptown',
          type: 'Residential',
          status: 'Active',
          latitude: 40.7589,
          longitude: -73.9851,
          description: 'High-rise residential building',
          imageUrl: 'https://example.com/property2.jpg',
          createdAt: DateTime.now().subtract(const Duration(days: 60)),
          updatedAt: DateTime.now(),
        ),
        PropertyModel(
          id: 'prop3',
          name: 'Shopping Mall Central',
          address: '789 Commerce Blvd, Central',
          type: 'Retail',
          status: 'Maintenance',
          latitude: 40.7505,
          longitude: -73.9934,
          description: 'Large shopping center with multiple tenants',
          imageUrl: 'https://example.com/property3.jpg',
          createdAt: DateTime.now().subtract(const Duration(days: 90)),
          updatedAt: DateTime.now(),
        ),
      ];

      // Apply search filter if provided
      if (search != null && search.isNotEmpty) {
        return mockProperties
            .where((property) =>
                property.name.toLowerCase().contains(search.toLowerCase()) ||
                property.address.toLowerCase().contains(search.toLowerCase()))
            .toList();
      }

      return mockProperties;
    } catch (e) {
      throw Exception('Failed to get properties: $e');
    }
  }

  @override
  Future<PropertyModel> getPropertyDetails(String propertyId) async {
    try {
      // Mock property details for demo purposes
      await Future.delayed(const Duration(milliseconds: 500));

      return PropertyModel(
        id: propertyId,
        name: 'Downtown Office Complex',
        address: '123 Main St, Downtown',
        type: 'Commercial',
        status: 'Active',
        latitude: 40.7128,
        longitude: -74.0060,
        description: 'Modern office complex with 24/7 power backup and advanced security systems',
        imageUrl: 'https://example.com/property1.jpg',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        metadata: {
          'floors': 15,
          'totalArea': 50000,
          'tenants': 25,
          'parkingSpaces': 200,
        },
      );
    } catch (e) {
      throw Exception('Failed to get property details: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getPropertyStatus(String propertyId) async {
    try {
      // Mock property status for demo purposes
      await Future.delayed(const Duration(milliseconds: 300));

      return {
        'powerStatus': 'Online',
        'generatorStatus': 'Standby',
        'fuelLevel': 85.5,
        'securityStatus': 'Armed',
        'maintenanceIssues': 2,
        'lastUpdate': DateTime.now().toIso8601String(),
        'occupancyRate': 92.5,
        'energyConsumption': {
          'current': 450.2,
          'average': 425.8,
          'peak': 520.1,
        },
      };
    } catch (e) {
      throw Exception('Failed to get property status: $e');
    }
  }
}
