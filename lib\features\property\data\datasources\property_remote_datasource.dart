import '../../../../core/network/api_client.dart';
import '../models/property_model.dart';

abstract class PropertyRemoteDataSource {
  Future<List<PropertyModel>> getProperties(int page, int limit, String? search);
  Future<PropertyModel> getPropertyDetails(String propertyId);
  Future<Map<String, dynamic>> getPropertyStatus(String propertyId);
}

class PropertyRemoteDataSourceImpl implements PropertyRemoteDataSource {
  final ApiClient apiClient;

  PropertyRemoteDataSourceImpl(this.apiClient);

  @override
  Future<List<PropertyModel>> getProperties(int page, int limit, String? search) async {
    try {
      return await apiClient.getProperties(page, limit, search);
    } catch (e) {
      throw Exception('Failed to get properties: $e');
    }
  }

  @override
  Future<PropertyModel> getPropertyDetails(String propertyId) async {
    try {
      return await apiClient.getPropertyDetails(propertyId);
    } catch (e) {
      throw Exception('Failed to get property details: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getPropertyStatus(String propertyId) async {
    try {
      return await apiClient.getPropertyStatus(propertyId);
    } catch (e) {
      throw Exception('Failed to get property status: $e');
    }
  }
}
