import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../config/app_config.dart';

class StorageService {
  static late Box _authBox;
  static late Box _propertyBox;
  static late Box _fuelBox;
  static late Box _maintenanceBox;
  static late Box _attendanceBox;
  static late Box _ottBox;
  
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  static Future<void> init() async {
    _authBox = await Hive.openBox(AppConfig.authBoxName);
    _propertyBox = await Hive.openBox(AppConfig.propertyBoxName);
    _fuelBox = await Hive.openBox(AppConfig.fuelBoxName);
    _maintenanceBox = await Hive.openBox(AppConfig.maintenanceBoxName);
    _attendanceBox = await Hive.openBox(AppConfig.attendanceBoxName);
    _ottBox = await Hive.openBox(AppConfig.ottBoxName);
  }

  // Secure storage methods (for sensitive data like tokens)
  Future<void> writeSecure(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  Future<String?> readSecure(String key) async {
    return await _secureStorage.read(key: key);
  }

  Future<void> deleteSecure(String key) async {
    await _secureStorage.delete(key: key);
  }

  Future<void> clearSecure() async {
    await _secureStorage.deleteAll();
  }

  // Auth box methods
  Future<void> saveAuthData(String key, dynamic value) async {
    await _authBox.put(key, value);
  }

  T? getAuthData<T>(String key) {
    return _authBox.get(key);
  }

  Future<void> deleteAuthData(String key) async {
    await _authBox.delete(key);
  }

  Future<void> clearAuthData() async {
    await _authBox.clear();
  }

  // Property box methods
  Future<void> saveProperty(String key, dynamic value) async {
    await _propertyBox.put(key, value);
  }

  Future<void> saveProperties(Map<String, dynamic> properties) async {
    await _propertyBox.putAll(properties);
  }

  T? getProperty<T>(String key) {
    return _propertyBox.get(key);
  }

  List<T> getAllProperties<T>() {
    return _propertyBox.values.cast<T>().toList();
  }

  Future<void> deleteProperty(String key) async {
    await _propertyBox.delete(key);
  }

  Future<void> clearProperties() async {
    await _propertyBox.clear();
  }

  // Fuel box methods
  Future<void> saveFuelData(String key, dynamic value) async {
    await _fuelBox.put(key, value);
  }

  T? getFuelData<T>(String key) {
    return _fuelBox.get(key);
  }

  List<T> getAllFuelData<T>() {
    return _fuelBox.values.cast<T>().toList();
  }

  Future<void> deleteFuelData(String key) async {
    await _fuelBox.delete(key);
  }

  Future<void> clearFuelData() async {
    await _fuelBox.clear();
  }

  // Maintenance box methods
  Future<void> saveMaintenanceData(String key, dynamic value) async {
    await _maintenanceBox.put(key, value);
  }

  T? getMaintenanceData<T>(String key) {
    return _maintenanceBox.get(key);
  }

  List<T> getAllMaintenanceData<T>() {
    return _maintenanceBox.values.cast<T>().toList();
  }

  Future<void> deleteMaintenanceData(String key) async {
    await _maintenanceBox.delete(key);
  }

  Future<void> clearMaintenanceData() async {
    await _maintenanceBox.clear();
  }

  // Attendance box methods
  Future<void> saveAttendanceData(String key, dynamic value) async {
    await _attendanceBox.put(key, value);
  }

  T? getAttendanceData<T>(String key) {
    return _attendanceBox.get(key);
  }

  List<T> getAllAttendanceData<T>() {
    return _attendanceBox.values.cast<T>().toList();
  }

  Future<void> deleteAttendanceData(String key) async {
    await _attendanceBox.delete(key);
  }

  Future<void> clearAttendanceData() async {
    await _attendanceBox.clear();
  }

  // OTT box methods
  Future<void> saveOttData(String key, dynamic value) async {
    await _ottBox.put(key, value);
  }

  T? getOttData<T>(String key) {
    return _ottBox.get(key);
  }

  List<T> getAllOttData<T>() {
    return _ottBox.values.cast<T>().toList();
  }

  Future<void> deleteOttData(String key) async {
    await _ottBox.delete(key);
  }

  Future<void> clearOttData() async {
    await _ottBox.clear();
  }

  // General utility methods
  Future<void> clearAllData() async {
    await Future.wait([
      clearAuthData(),
      clearProperties(),
      clearFuelData(),
      clearMaintenanceData(),
      clearAttendanceData(),
      clearOttData(),
      clearSecure(),
    ]);
  }

  bool hasData(String boxName, String key) {
    switch (boxName) {
      case AppConfig.authBoxName:
        return _authBox.containsKey(key);
      case AppConfig.propertyBoxName:
        return _propertyBox.containsKey(key);
      case AppConfig.fuelBoxName:
        return _fuelBox.containsKey(key);
      case AppConfig.maintenanceBoxName:
        return _maintenanceBox.containsKey(key);
      case AppConfig.attendanceBoxName:
        return _attendanceBox.containsKey(key);
      case AppConfig.ottBoxName:
        return _ottBox.containsKey(key);
      default:
        return false;
    }
  }

  int getDataCount(String boxName) {
    switch (boxName) {
      case AppConfig.authBoxName:
        return _authBox.length;
      case AppConfig.propertyBoxName:
        return _propertyBox.length;
      case AppConfig.fuelBoxName:
        return _fuelBox.length;
      case AppConfig.maintenanceBoxName:
        return _maintenanceBox.length;
      case AppConfig.attendanceBoxName:
        return _attendanceBox.length;
      case AppConfig.ottBoxName:
        return _ottBox.length;
      default:
        return 0;
    }
  }
}
