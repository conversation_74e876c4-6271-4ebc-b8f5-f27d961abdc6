import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'login_request_model.g.dart';

@JsonSerializable()
class LoginRequestModel extends Equatable {
  final String email;
  final String password;
  final bool rememberMe;
  final String? deviceId;
  final String? fcmToken;

  const LoginRequestModel({
    required this.email,
    required this.password,
    this.rememberMe = false,
    this.deviceId,
    this.fcmToken,
  });

  factory LoginRequestModel.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestModelToJson(this);

  LoginRequestModel copyWith({
    String? email,
    String? password,
    bool? rememberMe,
    String? deviceId,
    String? fcmToken,
  }) {
    return LoginRequestModel(
      email: email ?? this.email,
      password: password ?? this.password,
      rememberMe: rememberMe ?? this.rememberMe,
      deviceId: deviceId ?? this.deviceId,
      fcmToken: fcmToken ?? this.fcmToken,
    );
  }

  @override
  List<Object?> get props => [email, password, rememberMe, deviceId, fcmToken];

  @override
  String toString() {
    return 'LoginRequestModel(email: $email, rememberMe: $rememberMe)';
  }
}
