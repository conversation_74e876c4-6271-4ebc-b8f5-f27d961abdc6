# Property Management Flutter App

A comprehensive Flutter mobile application for Property Management System that integrates with the existing Next.js backend. The app provides field workers, managers, and administrators with mobile access to property management functions, real-time updates, and offline capabilities.

## 🚀 Features

### Core Functionality
- **Authentication & Authorization**: Role-based access (<PERSON><PERSON>, Manager, Field Worker, Guest)
- **Property Management**: View and manage multiple properties from mobile devices
- **Real-time Updates**: Live status updates for generator fuel, maintenance, security, and OTT services
- **Offline Support**: Critical functions work without internet connectivity
- **Push Notifications**: Alerts for maintenance issues, fuel levels, and escalations
- **Camera Integration**: Photo capture for maintenance reports and fuel level documentation
- **GPS Integration**: Location tracking for site visits and attendance
- **Barcode/QR Scanning**: Equipment identification and tracking

### Target Users
1. **Field Workers**: On-site maintenance, fuel updates, attendance tracking
2. **Property Managers**: Property oversight, issue management, reporting
3. **Administrators**: Full system access, user management, configuration
4. **Security Personnel**: CCTV monitoring, incident reporting

## 🏗️ Architecture

The app follows Clean Architecture principles with:
- **Presentation Layer**: BLoC pattern for state management
- **Domain Layer**: Use cases and entities
- **Data Layer**: Repositories, data sources (remote & local)
- **Core Layer**: Services, utilities, and configurations

### Key Technologies
- **Flutter**: ^3.16.0
- **State Management**: flutter_bloc ^8.1.3
- **API Integration**: dio ^5.3.2, retrofit ^4.0.3
- **Local Storage**: hive ^2.2.3, flutter_secure_storage ^9.0.0
- **Authentication**: JWT with biometric support
- **Camera**: camera ^0.10.5+5, image_picker ^1.0.4
- **Location**: geolocator ^10.1.0, google_maps_flutter ^2.5.0
- **Notifications**: firebase_messaging ^14.7.6
- **Charts**: fl_chart ^0.64.0

## 📱 App Modules

### 1. Authentication Module
- Login with email/password
- Biometric authentication (fingerprint/face)
- Remember login credentials
- Role-based dashboard routing
- Secure token storage
- Auto-logout on token expiry

### 2. Dashboard Module
- Property status overview cards
- Critical alerts and notifications
- Quick action buttons
- Real-time status indicators
- Pull-to-refresh functionality
- Offline status indicators

### 3. Property Management Module
- Property list with search/filter
- Property detail views
- Status monitoring (fuel, maintenance, security)
- Photo documentation
- Issue reporting with camera integration
- GPS location verification

### 4. Generator Fuel Module
- Current fuel level display
- Fuel level updates with photo proof
- Diesel addition logging
- Fuel consumption analytics
- Low fuel alerts
- Fuel history timeline

### 5. Maintenance Module
- Issue reporting with photos
- Maintenance task assignments
- Work order management
- Before/after photo documentation
- Issue status tracking
- Escalation notifications

### 6. Attendance Module
- GPS-based check-in/check-out
- Site visit logging
- Attendance reports
- Location verification
- Offline attendance sync
- Time tracking

### 7. Security Module
- CCTV status monitoring
- Security incident reporting
- Camera location maps
- Security alerts
- Incident photo documentation

### 8. OTT Services Module
- Service status monitoring
- Service issue reporting
- Configuration management
- Service analytics
- Connectivity testing

## 🛠️ Setup Instructions

### Prerequisites
- Flutter SDK (>=3.16.0)
- Dart SDK (>=3.0.0)
- Android Studio / Xcode
- Firebase project setup
- Google Maps API key

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd property_management_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Firebase Setup**
   - Create a Firebase project
   - Add Android and iOS apps
   - Download `google-services.json` (Android) and `GoogleService-Info.plist` (iOS)
   - Place them in the appropriate directories

5. **Google Maps Setup**
   - Get Google Maps API key
   - Add to `android/app/src/main/AndroidManifest.xml`
   - Add to `ios/Runner/AppDelegate.swift`

6. **Environment Configuration**
   - Update API base URLs in `lib/core/config/app_config.dart`
   - Configure environment variables

### Running the App

```bash
# Development
flutter run --dart-define=ENVIRONMENT=development

# Staging
flutter run --dart-define=ENVIRONMENT=staging

# Production
flutter run --dart-define=ENVIRONMENT=production
```

## 🔧 Configuration

### API Configuration
Update the API endpoints in `lib/core/config/app_config.dart`:

```dart
static const String baseUrl = 'https://your-backend-api.com/api';
static const String devBaseUrl = 'http://localhost:3000/api';
static const String stagingBaseUrl = 'https://staging-api.com/api';
```

### Firebase Configuration
1. Add your Firebase configuration files
2. Update notification channels in `lib/core/services/notification_service.dart`
3. Configure FCM topics based on user roles

### Google Maps Configuration
Add your Google Maps API key:

**Android** (`android/app/src/main/AndroidManifest.xml`):
```xml
<meta-data android:name="com.google.android.geo.API_KEY"
           android:value="YOUR_GOOGLE_MAPS_API_KEY"/>
```

**iOS** (`ios/Runner/AppDelegate.swift`):
```swift
GMSServices.provideAPIKey("YOUR_GOOGLE_MAPS_API_KEY")
```

## 📊 State Management

The app uses BLoC pattern for state management:

```dart
// Example BLoC usage
BlocProvider<AuthBloc>(
  create: (context) => getIt<AuthBloc>()..add(AuthCheckRequested()),
  child: MyApp(),
)
```

## 💾 Data Storage

### Local Storage
- **Hive**: For offline data caching
- **Secure Storage**: For sensitive data (tokens, credentials)
- **Shared Preferences**: For app settings

### Data Synchronization
- Automatic sync when online
- Queue offline actions for later sync
- Conflict resolution with server timestamps
- Background sync capabilities

## 🔔 Push Notifications

### Notification Types
- **Critical Alerts**: Fuel low, Security breach
- **Maintenance Reminders**: Task assignments, deadlines
- **Status Updates**: Property status changes
- **System Announcements**: App updates, maintenance

### Setup
1. Configure Firebase Cloud Messaging
2. Set up notification channels
3. Handle notification taps and routing
4. Subscribe to role-based topics

## 📸 Camera Integration

### Features
- Photo capture for maintenance reports
- Fuel level documentation
- Before/after photos
- Multiple photo uploads
- Image compression and optimization
- Photo annotation capabilities

## 🗺️ Location Services

### Features
- Site visit verification
- Attendance check-in/out
- Property location mapping
- Distance calculations
- Location-based notifications
- Geofencing for sites

## 🧪 Testing

### Running Tests
```bash
# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Widget tests
flutter test test/widget_test/
```

### Test Coverage
```bash
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

## 🚀 Building for Production

### Android
```bash
flutter build apk --release
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## 📱 Platform-Specific Features

### Android
- Material Design 3 components
- Adaptive icons
- Background services
- Android Auto integration (future)

### iOS
- Cupertino design elements
- iOS shortcuts
- Siri integration (future)
- Apple Watch companion (future)

## 🔒 Security

### Authentication
- JWT token-based authentication
- Biometric authentication support
- Secure token storage
- Auto-refresh tokens

### Data Protection
- Encrypted local storage
- Secure API communication (HTTPS)
- Certificate pinning (recommended)
- Data validation and sanitization

## 📈 Performance

### Optimization
- Lazy loading for lists
- Image caching and compression
- Efficient state management
- Memory leak prevention
- Battery optimization

## 🐛 Troubleshooting

### Common Issues

1. **Build Errors**
   - Run `flutter clean && flutter pub get`
   - Check Flutter and Dart SDK versions
   - Verify all dependencies are compatible

2. **Firebase Issues**
   - Ensure configuration files are in correct locations
   - Check package names match Firebase project
   - Verify SHA-1 fingerprints for Android

3. **Location Issues**
   - Check permissions in manifest files
   - Verify location services are enabled
   - Test on physical device (not simulator)

4. **Camera Issues**
   - Check camera permissions
   - Test on physical device
   - Verify camera hardware availability

## 📚 Documentation

- [Flutter Documentation](https://docs.flutter.dev/)
- [BLoC Pattern Guide](https://bloclibrary.dev/)
- [Firebase for Flutter](https://firebase.flutter.dev/)
- [Google Maps Flutter](https://pub.dev/packages/google_maps_flutter)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation and troubleshooting guide

---

**Note**: This is a comprehensive Flutter application that requires proper backend integration and configuration. Make sure to update all configuration files with your actual API endpoints, Firebase configuration, and API keys before deploying to production.
