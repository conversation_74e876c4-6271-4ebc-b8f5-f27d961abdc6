import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';

import '../config/app_config.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;

  static Future<void> initialize() async {
    // Initialize local notifications
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels
    await _createNotificationChannels();

    // Initialize Firebase messaging
    await _initializeFirebaseMessaging();
  }

  static Future<void> _createNotificationChannels() async {
    // Critical alerts channel
    const criticalChannel = AndroidNotificationChannel(
      AppConfig.criticalAlertsChannel,
      'Critical Alerts',
      description: 'Critical system alerts and emergencies',
      importance: Importance.max,
      priority: Priority.high,
      enableVibration: true,
      enableLights: true,
      ledColor: Color(0xFFEF4444),
    );

    // Maintenance channel
    const maintenanceChannel = AndroidNotificationChannel(
      AppConfig.maintenanceChannel,
      'Maintenance',
      description: 'Maintenance reminders and updates',
      importance: Importance.high,
      priority: Priority.high,
      enableVibration: true,
    );

    // General channel
    const generalChannel = AndroidNotificationChannel(
      AppConfig.generalChannel,
      'General',
      description: 'General notifications and updates',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(criticalChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(maintenanceChannel);

    await _localNotifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(generalChannel);
  }

  static Future<void> _initializeFirebaseMessaging() async {
    // Request permission
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      debugPrint('User granted permission');
    } else {
      debugPrint('User declined or has not accepted permission');
    }

    // Get FCM token
    final token = await _firebaseMessaging.getToken();
    debugPrint('FCM Token: $token');

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    debugPrint('Handling a background message: ${message.messageId}');
    await showNotification(
      title: message.notification?.title ?? 'New Message',
      body: message.notification?.body ?? '',
      payload: message.data.toString(),
      channelId: _getChannelFromData(message.data),
    );
  }

  static void _handleForegroundMessage(RemoteMessage message) {
    debugPrint('Got a message whilst in the foreground!');
    debugPrint('Message data: ${message.data}');

    if (message.notification != null) {
      showNotification(
        title: message.notification!.title ?? 'New Message',
        body: message.notification!.body ?? '',
        payload: message.data.toString(),
        channelId: _getChannelFromData(message.data),
      );
    }
  }

  static void _handleNotificationTap(RemoteMessage message) {
    debugPrint('A new onMessageOpenedApp event was published!');
    // Handle navigation based on message data
    _navigateFromNotification(message.data);
  }

  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    // Handle navigation based on payload
    if (response.payload != null) {
      _navigateFromPayload(response.payload!);
    }
  }

  static String _getChannelFromData(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    switch (type) {
      case 'critical':
        return AppConfig.criticalAlertsChannel;
      case 'maintenance':
        return AppConfig.maintenanceChannel;
      default:
        return AppConfig.generalChannel;
    }
  }

  static void _navigateFromNotification(Map<String, dynamic> data) {
    // Implement navigation logic based on notification data
    final type = data['type'] as String?;
    final id = data['id'] as String?;
    
    // This would typically use a navigation service or router
    debugPrint('Navigate to: $type with id: $id');
  }

  static void _navigateFromPayload(String payload) {
    // Implement navigation logic based on payload
    debugPrint('Navigate from payload: $payload');
  }

  static Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
    String channelId = AppConfig.generalChannel,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      channelId,
      _getChannelName(channelId),
      channelDescription: _getChannelDescription(channelId),
      importance: _getImportance(channelId),
      priority: _getPriority(channelId),
      enableVibration: true,
      enableLights: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      details,
      payload: payload,
    );
  }

  static String _getChannelName(String channelId) {
    switch (channelId) {
      case AppConfig.criticalAlertsChannel:
        return 'Critical Alerts';
      case AppConfig.maintenanceChannel:
        return 'Maintenance';
      default:
        return 'General';
    }
  }

  static String _getChannelDescription(String channelId) {
    switch (channelId) {
      case AppConfig.criticalAlertsChannel:
        return 'Critical system alerts and emergencies';
      case AppConfig.maintenanceChannel:
        return 'Maintenance reminders and updates';
      default:
        return 'General notifications and updates';
    }
  }

  static Importance _getImportance(String channelId) {
    switch (channelId) {
      case AppConfig.criticalAlertsChannel:
        return Importance.max;
      case AppConfig.maintenanceChannel:
        return Importance.high;
      default:
        return Importance.defaultImportance;
    }
  }

  static Priority _getPriority(String channelId) {
    switch (channelId) {
      case AppConfig.criticalAlertsChannel:
        return Priority.high;
      case AppConfig.maintenanceChannel:
        return Priority.high;
      default:
        return Priority.defaultPriority;
    }
  }

  static Future<String?> getFCMToken() async {
    return await _firebaseMessaging.getToken();
  }

  static Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
  }

  static Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
  }

  static Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  static Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }
}
