import 'package:equatable/equatable.dart';

abstract class Failure extends Equatable {
  final String message;
  final int? statusCode;

  const Failure({
    required this.message,
    this.statusCode,
  });

  @override
  List<Object?> get props => [message, statusCode];

  @override
  String toString() => message;
}

class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.statusCode,
  });
}

class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.statusCode,
  });
}

class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.statusCode,
  });
}

class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.statusCode,
  });
}

class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.statusCode,
  });
}

class LocationFailure extends Failure {
  const LocationFailure({
    required super.message,
    super.statusCode,
  });
}

class CameraFailure extends Failure {
  const CameraFailure({
    required super.message,
    super.statusCode,
  });
}

class StorageFailure extends Failure {
  const StorageFailure({
    required super.message,
    super.statusCode,
  });
}

class PermissionFailure extends Failure {
  const PermissionFailure({
    required super.message,
    super.statusCode,
  });
}

class SyncFailure extends Failure {
  const SyncFailure({
    required super.message,
    super.statusCode,
  });
}

class NotificationFailure extends Failure {
  const NotificationFailure({
    required super.message,
    super.statusCode,
  });
}

class QRCodeFailure extends Failure {
  const QRCodeFailure({
    required super.message,
    super.statusCode,
  });
}
