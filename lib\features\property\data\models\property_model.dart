import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'property_model.g.dart';

@JsonSerializable()
class PropertyModel extends Equatable {
  final String id;
  final String name;
  final String address;
  final String type;
  final String status;
  final double? latitude;
  final double? longitude;
  final String? description;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  const PropertyModel({
    required this.id,
    required this.name,
    required this.address,
    required this.type,
    required this.status,
    this.latitude,
    this.longitude,
    this.description,
    this.imageUrl,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  factory PropertyModel.fromJson(Map<String, dynamic> json) =>
      _$PropertyModelFromJson(json);

  Map<String, dynamic> toJson() => _$PropertyModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        address,
        type,
        status,
        latitude,
        longitude,
        description,
        imageUrl,
        createdAt,
        updatedAt,
        metadata,
      ];
}
