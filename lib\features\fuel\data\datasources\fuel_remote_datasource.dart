import '../../../../core/network/api_client.dart';
import '../models/fuel_status_model.dart';
import '../models/fuel_update_request_model.dart';

abstract class FuelRemoteDataSource {
  Future<FuelStatusModel> getFuelStatus(String propertyId);
  Future<FuelStatusModel> updateFuelLevel(FuelUpdateRequestModel request);
  Future<List<FuelStatusModel>> getFuelHistory(String propertyId, int page, int limit);
}

class FuelRemoteDataSourceImpl implements FuelRemoteDataSource {
  final ApiClient apiClient;

  FuelRemoteDataSourceImpl(this.apiClient);

  @override
  Future<FuelStatusModel> getFuelStatus(String propertyId) async {
    try {
      return await apiClient.getFuelStatus(propertyId);
    } catch (e) {
      throw Exception('Failed to get fuel status: $e');
    }
  }

  @override
  Future<FuelStatusModel> updateFuelLevel(FuelUpdateRequestModel request) async {
    try {
      return await apiClient.updateFuelLevel(request);
    } catch (e) {
      throw Exception('Failed to update fuel level: $e');
    }
  }

  @override
  Future<List<FuelStatusModel>> getFuelHistory(String propertyId, int page, int limit) async {
    try {
      return await apiClient.getFuelHistory(propertyId, page, limit);
    } catch (e) {
      throw Exception('Failed to get fuel history: $e');
    }
  }
}
