import '../models/fuel_status_model.dart';
import '../models/fuel_update_request_model.dart';

abstract class FuelRemoteDataSource {
  Future<FuelStatusModel> getFuelStatus(String propertyId);
  Future<FuelStatusModel> updateFuelLevel(FuelUpdateRequestModel request);
  Future<List<FuelStatusModel>> getFuelHistory(String propertyId, int page, int limit);
}

class FuelRemoteDataSourceImpl implements FuelRemoteDataSource {
  FuelRemoteDataSourceImpl();

  @override
  Future<FuelStatusModel> getFuelStatus(String propertyId) async {
    try {
      // Mock fuel status for demo purposes
      await Future.delayed(const Duration(milliseconds: 800));

      return FuelStatusModel(
        id: 'fuel_${propertyId}_${DateTime.now().millisecondsSinceEpoch}',
        propertyId: propertyId,
        currentLevel: 750.5,
        capacity: 1000.0,
        unit: 'Liters',
        lastUpdated: DateTime.now().subtract(const Duration(hours: 2)),
        lastUpdatedBy: '<PERSON>',
        consumptionRate: 25.5,
        estimatedEmptyDate: DateTime.now().add(const Duration(days: 12)),
        status: 'Normal',
        images: [
          'https://example.com/fuel_image1.jpg',
          'https://example.com/fuel_image2.jpg',
        ],
        notes: 'Fuel level is within normal range',
        metadata: {
          'tankType': 'Underground',
          'fuelGrade': 'Diesel',
          'supplier': 'ABC Fuel Company',
        },
      );
    } catch (e) {
      throw Exception('Failed to get fuel status: $e');
    }
  }

  @override
  Future<FuelStatusModel> updateFuelLevel(FuelUpdateRequestModel request) async {
    try {
      // Mock fuel update for demo purposes
      await Future.delayed(const Duration(seconds: 1));

      return FuelStatusModel(
        id: 'fuel_${request.propertyId}_${DateTime.now().millisecondsSinceEpoch}',
        propertyId: request.propertyId,
        currentLevel: request.currentLevel,
        capacity: 1000.0,
        unit: 'Liters',
        lastUpdated: request.timestamp,
        lastUpdatedBy: 'Current User',
        consumptionRate: 25.5,
        estimatedEmptyDate: DateTime.now().add(const Duration(days: 15)),
        status: request.currentLevel < 250 ? 'Low' : 'Normal',
        images: request.images,
        notes: request.notes ?? 'Fuel level updated successfully',
        metadata: {
          'updateMethod': 'Mobile App',
          'location': '${request.latitude}, ${request.longitude}',
        },
      );
    } catch (e) {
      throw Exception('Failed to update fuel level: $e');
    }
  }

  @override
  Future<List<FuelStatusModel>> getFuelHistory(String propertyId, int page, int limit) async {
    try {
      // Mock fuel history for demo purposes
      await Future.delayed(const Duration(milliseconds: 600));

      final history = <FuelStatusModel>[];
      for (int i = 0; i < limit; i++) {
        history.add(FuelStatusModel(
          id: 'fuel_history_${propertyId}_$i',
          propertyId: propertyId,
          currentLevel: 800.0 - (i * 50.0),
          capacity: 1000.0,
          unit: 'Liters',
          lastUpdated: DateTime.now().subtract(Duration(days: i + 1)),
          lastUpdatedBy: 'User ${i + 1}',
          consumptionRate: 25.5,
          estimatedEmptyDate: DateTime.now().add(Duration(days: 10 + i)),
          status: 'Normal',
          notes: 'Historical fuel record $i',
        ));
      }

      return history;
    } catch (e) {
      throw Exception('Failed to get fuel history: $e');
    }
  }
}
