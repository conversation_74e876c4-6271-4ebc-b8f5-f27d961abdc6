// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'maintenance_issue_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MaintenanceIssueModel _$MaintenanceIssueModelFromJson(
        Map<String, dynamic> json) =>
    MaintenanceIssueModel(
      id: json['id'] as String,
      propertyId: json['propertyId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      priority: json['priority'] as String,
      status: json['status'] as String,
      category: json['category'] as String,
      reportedBy: json['reportedBy'] as String,
      assignedTo: json['assignedTo'] as String?,
      reportedAt: DateTime.parse(json['reportedAt'] as String),
      scheduledDate: json['scheduledDate'] == null
          ? null
          : DateTime.parse(json['scheduledDate'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      notes: json['notes'] as String?,
      estimatedCost: (json['estimatedCost'] as num?)?.toDouble(),
      actualCost: (json['actualCost'] as num?)?.toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$MaintenanceIssueModelToJson(
        MaintenanceIssueModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'propertyId': instance.propertyId,
      'title': instance.title,
      'description': instance.description,
      'priority': instance.priority,
      'status': instance.status,
      'category': instance.category,
      'reportedBy': instance.reportedBy,
      'assignedTo': instance.assignedTo,
      'reportedAt': instance.reportedAt.toIso8601String(),
      'scheduledDate': instance.scheduledDate?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'images': instance.images,
      'notes': instance.notes,
      'estimatedCost': instance.estimatedCost,
      'actualCost': instance.actualCost,
      'metadata': instance.metadata,
    };
