import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../domain/usecases/get_properties_usecase.dart';
import '../../domain/usecases/get_property_details_usecase.dart';
import '../../data/models/property_model.dart';

// Events
abstract class PropertyEvent extends Equatable {
  const PropertyEvent();

  @override
  List<Object?> get props => [];
}

class PropertyListRequested extends PropertyEvent {
  final int page;
  final int limit;
  final String? search;

  const PropertyListRequested({
    required this.page,
    required this.limit,
    this.search,
  });

  @override
  List<Object?> get props => [page, limit, search];
}

class PropertyDetailsRequested extends PropertyEvent {
  final String propertyId;

  const PropertyDetailsRequested({required this.propertyId});

  @override
  List<Object?> get props => [propertyId];
}

class PropertyRefreshRequested extends PropertyEvent {}

// States
abstract class PropertyState extends Equatable {
  const PropertyState();

  @override
  List<Object?> get props => [];
}

class PropertyInitial extends PropertyState {}

class PropertyLoading extends PropertyState {}

class PropertyListLoaded extends PropertyState {
  final List<PropertyModel> properties;
  final bool hasReachedMax;

  const PropertyListLoaded({
    required this.properties,
    this.hasReachedMax = false,
  });

  @override
  List<Object?> get props => [properties, hasReachedMax];

  PropertyListLoaded copyWith({
    List<PropertyModel>? properties,
    bool? hasReachedMax,
  }) {
    return PropertyListLoaded(
      properties: properties ?? this.properties,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
    );
  }
}

class PropertyDetailsLoaded extends PropertyState {
  final PropertyModel property;

  const PropertyDetailsLoaded({required this.property});

  @override
  List<Object?> get props => [property];
}

class PropertyError extends PropertyState {
  final String message;

  const PropertyError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class PropertyBloc extends Bloc<PropertyEvent, PropertyState> {
  final GetPropertiesUseCase getPropertiesUseCase;
  final GetPropertyDetailsUseCase getPropertyDetailsUseCase;

  PropertyBloc({
    required this.getPropertiesUseCase,
    required this.getPropertyDetailsUseCase,
  }) : super(PropertyInitial()) {
    on<PropertyListRequested>(_onPropertyListRequested);
    on<PropertyDetailsRequested>(_onPropertyDetailsRequested);
    on<PropertyRefreshRequested>(_onPropertyRefreshRequested);
  }

  Future<void> _onPropertyListRequested(
    PropertyListRequested event,
    Emitter<PropertyState> emit,
  ) async {
    if (event.page == 1) {
      emit(PropertyLoading());
    }

    final result = await getPropertiesUseCase(GetPropertiesParams(
      page: event.page,
      limit: event.limit,
      search: event.search,
    ));

    result.fold(
      (failure) => emit(PropertyError(message: failure.message)),
      (properties) {
        final currentState = state;
        if (currentState is PropertyListLoaded && event.page > 1) {
          // Load more
          final updatedProperties = List<PropertyModel>.from(currentState.properties)
            ..addAll(properties);
          emit(PropertyListLoaded(
            properties: updatedProperties,
            hasReachedMax: properties.length < event.limit,
          ));
        } else {
          // Initial load
          emit(PropertyListLoaded(
            properties: properties,
            hasReachedMax: properties.length < event.limit,
          ));
        }
      },
    );
  }

  Future<void> _onPropertyDetailsRequested(
    PropertyDetailsRequested event,
    Emitter<PropertyState> emit,
  ) async {
    emit(PropertyLoading());

    final result = await getPropertyDetailsUseCase(
      GetPropertyDetailsParams(propertyId: event.propertyId),
    );

    result.fold(
      (failure) => emit(PropertyError(message: failure.message)),
      (property) => emit(PropertyDetailsLoaded(property: property)),
    );
  }

  Future<void> _onPropertyRefreshRequested(
    PropertyRefreshRequested event,
    Emitter<PropertyState> emit,
  ) async {
    // Refresh the current list
    add(const PropertyListRequested(page: 1, limit: 20));
  }
}
