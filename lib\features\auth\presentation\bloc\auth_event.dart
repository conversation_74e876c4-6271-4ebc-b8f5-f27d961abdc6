import 'package:equatable/equatable.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthCheckRequested extends AuthEvent {}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;
  final bool rememberMe;

  const AuthLoginRequested({
    required this.email,
    required this.password,
    this.rememberMe = false,
  });

  @override
  List<Object?> get props => [email, password, rememberMe];
}

class AuthLogoutRequested extends AuthEvent {}

class AuthBiometricLoginRequested extends AuthEvent {}

class AuthRefreshTokenRequested extends AuthEvent {}

class AuthPasswordChangeRequested extends AuthEvent {
  final String currentPassword;
  final String newPassword;

  const AuthPasswordChangeRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

class AuthForgotPasswordRequested extends AuthEvent {
  final String email;

  const AuthForgotPasswordRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

class AuthResetPasswordRequested extends AuthEvent {
  final String token;
  final String newPassword;

  const AuthResetPasswordRequested({
    required this.token,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [token, newPassword];
}

class AuthProfileUpdateRequested extends AuthEvent {
  final String? firstName;
  final String? lastName;
  final String? phone;
  final String? avatar;

  const AuthProfileUpdateRequested({
    this.firstName,
    this.lastName,
    this.phone,
    this.avatar,
  });

  @override
  List<Object?> get props => [firstName, lastName, phone, avatar];
}

class AuthBiometricToggleRequested extends AuthEvent {
  final bool enable;

  const AuthBiometricToggleRequested({required this.enable});

  @override
  List<Object?> get props => [enable];
}

class AuthFCMTokenUpdateRequested extends AuthEvent {
  final String token;

  const AuthFCMTokenUpdateRequested({required this.token});

  @override
  List<Object?> get props => [token];
}

class AuthDeleteAccountRequested extends AuthEvent {}

class AuthErrorDismissed extends AuthEvent {}
