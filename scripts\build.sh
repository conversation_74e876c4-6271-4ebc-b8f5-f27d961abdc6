#!/bin/bash

# Property Management App Build Script

set -e

echo "🚀 Starting Property Management App Build Process..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

# Check Flutter version
print_status "Checking Flutter version..."
flutter --version

# Clean previous builds
print_status "Cleaning previous builds..."
flutter clean

# Get dependencies
print_status "Getting dependencies..."
flutter pub get

# Generate code
print_status "Generating code..."
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run tests
print_status "Running tests..."
flutter test

# Analyze code
print_status "Analyzing code..."
flutter analyze

# Check for environment argument
ENVIRONMENT=${1:-development}
print_status "Building for environment: $ENVIRONMENT"

# Build based on environment
case $ENVIRONMENT in
    "development")
        print_status "Building development APK..."
        flutter build apk --debug --dart-define=ENVIRONMENT=development
        ;;
    "staging")
        print_status "Building staging APK..."
        flutter build apk --release --dart-define=ENVIRONMENT=staging
        ;;
    "production")
        print_status "Building production APK and App Bundle..."
        flutter build apk --release --dart-define=ENVIRONMENT=production
        flutter build appbundle --release --dart-define=ENVIRONMENT=production
        ;;
    *)
        print_error "Invalid environment: $ENVIRONMENT"
        print_status "Valid environments: development, staging, production"
        exit 1
        ;;
esac

print_success "Build completed successfully! 🎉"

# Show build artifacts
print_status "Build artifacts:"
if [ "$ENVIRONMENT" = "production" ]; then
    echo "  - APK: build/app/outputs/flutter-apk/app-release.apk"
    echo "  - App Bundle: build/app/outputs/bundle/release/app-release.aab"
else
    echo "  - APK: build/app/outputs/flutter-apk/app-$ENVIRONMENT.apk"
fi

print_status "Build script completed! ✅"
