// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardDataModel _$DashboardDataModelFromJson(Map<String, dynamic> json) =>
    DashboardDataModel(
      totalProperties: json['totalProperties'] as int,
      activeIssues: json['activeIssues'] as int,
      fuelAlerts: json['fuelAlerts'] as int,
      completedTasks: json['completedTasks'] as int,
      recentActivities: (json['recentActivities'] as List<dynamic>)
          .map((e) => RecentActivityModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      stats: json['stats'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$DashboardDataModelToJson(DashboardDataModel instance) =>
    <String, dynamic>{
      'totalProperties': instance.totalProperties,
      'activeIssues': instance.activeIssues,
      'fuelAlerts': instance.fuelAlerts,
      'completedTasks': instance.completedTasks,
      'recentActivities': instance.recentActivities,
      'stats': instance.stats,
    };

RecentActivityModel _$RecentActivityModelFromJson(Map<String, dynamic> json) =>
    RecentActivityModel(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: json['type'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      propertyId: json['propertyId'] as String?,
      userId: json['userId'] as String?,
    );

Map<String, dynamic> _$RecentActivityModelToJson(
        RecentActivityModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'type': instance.type,
      'timestamp': instance.timestamp.toIso8601String(),
      'propertyId': instance.propertyId,
      'userId': instance.userId,
    };
