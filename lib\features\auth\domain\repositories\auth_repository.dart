import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/user.dart';

abstract class AuthRepository {
  Future<Either<Failure, User>> login({
    required String email,
    required String password,
    bool rememberMe = false,
  });

  Future<Either<Failure, void>> logout();

  Future<Either<Failure, User>> getCurrentUser();

  Future<Either<Failure, bool>> isLoggedIn();

  Future<Either<Failure, User>> refreshToken();

  Future<Either<Failure, void>> updateFCMToken(String token);

  Future<Either<Failure, bool>> enableBiometric();

  Future<Either<Failure, bool>> disableBiometric();

  Future<Either<Failure, bool>> isBiometricEnabled();

  Future<Either<Failure, User>> authenticateWithBiometric();

  Future<Either<Failure, void>> changePassword({
    required String currentPassword,
    required String newPassword,
  });

  Future<Either<Failure, void>> forgotPassword(String email);

  Future<Either<Failure, void>> resetPassword({
    required String token,
    required String newPassword,
  });

  Future<Either<Failure, User>> updateProfile({
    String? firstName,
    String? lastName,
    String? phone,
    String? avatar,
  });

  Future<Either<Failure, void>> deleteAccount();
}
