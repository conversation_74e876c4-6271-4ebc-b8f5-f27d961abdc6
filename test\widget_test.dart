import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:property_management_app/main.dart';
import 'package:property_management_app/features/auth/presentation/bloc/auth_bloc.dart';
import 'package:property_management_app/features/auth/presentation/bloc/auth_event.dart';
import 'package:property_management_app/features/auth/presentation/bloc/auth_state.dart';

// Mock AuthBloc for testing
class MockAuthBloc extends AuthBloc {
  MockAuthBloc() : super(
    loginUseCase: MockLoginUseCase(),
    logoutUseCase: MockLogoutUseCase(),
    checkAuthStatusUseCase: MockCheckAuthStatusUseCase(),
  );

  @override
  Stream<AuthState> mapEventToState(AuthEvent event) async* {
    if (event is AuthCheckRequested) {
      yield AuthUnauthenticated();
    }
  }
}

// Mock use cases
class MockLoginUseCase {
  // Mock implementation
}

class MockLogoutUseCase {
  // Mock implementation
}

class MockCheckAuthStatusUseCase {
  // Mock implementation
}

void main() {
  group('Property Management App Tests', () {
    testWidgets('App should start with splash screen', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(
        BlocProvider<AuthBloc>(
          create: (context) => MockAuthBloc(),
          child: const MaterialApp(
            home: Scaffold(
              body: Center(
                child: Text('Property Management'),
              ),
            ),
          ),
        ),
      );

      // Verify that the app title is displayed
      expect(find.text('Property Management'), findsOneWidget);
    });

    testWidgets('Should display loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          ),
        ),
      );

      // Verify that loading indicator is displayed
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('Should navigate to login when unauthenticated', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: Text('Login')),
            body: Column(
              children: [
                TextField(
                  decoration: InputDecoration(labelText: 'Email'),
                ),
                TextField(
                  decoration: InputDecoration(labelText: 'Password'),
                  obscureText: true,
                ),
                ElevatedButton(
                  onPressed: () {},
                  child: Text('Sign In'),
                ),
              ],
            ),
          ),
        ),
      );

      // Verify login elements are present
      expect(find.text('Login'), findsOneWidget);
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);
    });

    testWidgets('Should display dashboard when authenticated', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: Text('Dashboard')),
            body: Column(
              children: [
                Text('Welcome back!'),
                Text('Quick Stats'),
                Card(child: Text('Properties: 12')),
                Card(child: Text('Active Issues: 3')),
              ],
            ),
          ),
        ),
      );

      // Verify dashboard elements are present
      expect(find.text('Dashboard'), findsOneWidget);
      expect(find.text('Welcome back!'), findsOneWidget);
      expect(find.text('Quick Stats'), findsOneWidget);
      expect(find.text('Properties: 12'), findsOneWidget);
    });
  });

  group('Authentication Tests', () {
    testWidgets('Login form validation', (WidgetTester tester) async {
      final emailController = TextEditingController();
      final passwordController = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Form(
              child: Column(
                children: [
                  TextFormField(
                    controller: emailController,
                    decoration: InputDecoration(labelText: 'Email'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your email';
                      }
                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                        return 'Please enter a valid email';
                      }
                      return null;
                    },
                  ),
                  TextFormField(
                    controller: passwordController,
                    decoration: InputDecoration(labelText: 'Password'),
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your password';
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters';
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Test email validation
      await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
      await tester.pump();

      // Test password validation
      await tester.enterText(find.byType(TextFormField).last, '123');
      await tester.pump();

      expect(find.byType(TextFormField), findsNWidgets(2));
    });
  });

  group('Widget Tests', () {
    testWidgets('Custom button widget test', (WidgetTester tester) async {
      bool buttonPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ElevatedButton(
              onPressed: () {
                buttonPressed = true;
              },
              child: Text('Test Button'),
            ),
          ),
        ),
      );

      // Find and tap the button
      await tester.tap(find.text('Test Button'));
      await tester.pump();

      expect(buttonPressed, isTrue);
    });

    testWidgets('Custom text field widget test', (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TextField(
              controller: controller,
              decoration: InputDecoration(
                labelText: 'Test Field',
                hintText: 'Enter text here',
              ),
            ),
          ),
        ),
      );

      // Enter text and verify
      await tester.enterText(find.byType(TextField), 'Test input');
      expect(controller.text, 'Test input');
    });
  });

  group('Navigation Tests', () {
    testWidgets('Should navigate between screens', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          initialRoute: '/',
          routes: {
            '/': (context) => Scaffold(
              appBar: AppBar(title: Text('Home')),
              body: ElevatedButton(
                onPressed: () => Navigator.pushNamed(context, '/second'),
                child: Text('Go to Second'),
              ),
            ),
            '/second': (context) => Scaffold(
              appBar: AppBar(title: Text('Second')),
              body: Text('Second Screen'),
            ),
          },
        ),
      );

      // Verify initial screen
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Go to Second'), findsOneWidget);

      // Navigate to second screen
      await tester.tap(find.text('Go to Second'));
      await tester.pumpAndSettle();

      // Verify navigation
      expect(find.text('Second'), findsOneWidget);
      expect(find.text('Second Screen'), findsOneWidget);
    });
  });
}
