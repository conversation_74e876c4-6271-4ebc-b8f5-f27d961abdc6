import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'fuel_update_request_model.g.dart';

@JsonSerializable()
class FuelUpdateRequestModel extends Equatable {
  final String propertyId;
  final double currentLevel;
  final double? addedAmount;
  final String? notes;
  final List<String>? images;
  final double? latitude;
  final double? longitude;
  final DateTime timestamp;

  const FuelUpdateRequestModel({
    required this.propertyId,
    required this.currentLevel,
    this.addedAmount,
    this.notes,
    this.images,
    this.latitude,
    this.longitude,
    required this.timestamp,
  });

  factory FuelUpdateRequestModel.fromJson(Map<String, dynamic> json) =>
      _$FuelUpdateRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$FuelUpdateRequestModelToJson(this);

  @override
  List<Object?> get props => [
        propertyId,
        currentLevel,
        addedAmount,
        notes,
        images,
        latitude,
        longitude,
        timestamp,
      ];
}
