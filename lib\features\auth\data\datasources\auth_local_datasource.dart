import '../../../../core/config/app_config.dart';
import '../../../../core/services/storage_service.dart';
import '../models/user_model.dart';
import '../models/login_response_model.dart';

abstract class AuthLocalDataSource {
  Future<void> saveAuthData(LoginResponseModel loginResponse);
  Future<UserModel?> getCachedUser();
  Future<String?> getAccessToken();
  Future<String?> getRefreshToken();
  Future<bool> isLoggedIn();
  Future<void> clearAuthData();
  Future<void> updateUser(UserModel user);
  Future<void> setBiometricEnabled(bool enabled);
  Future<bool> isBiometricEnabled();
}

class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final StorageService storageService;

  AuthLocalDataSourceImpl(this.storageService);

  @override
  Future<void> saveAuthData(LoginResponseModel loginResponse) async {
    try {
      // Save tokens securely
      await storageService.writeSecure(
        AppConfig.accessTokenKey,
        loginResponse.accessToken,
      );
      await storageService.writeSecure(
        AppConfig.refreshTokenKey,
        loginResponse.refreshToken,
      );

      // Save user data
      await storageService.saveAuthData(
        AppConfig.userDataKey,
        loginResponse.user.toJson(),
      );

      // Save login timestamp
      await storageService.saveAuthData(
        'login_timestamp',
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      throw Exception('Failed to save auth data: $e');
    }
  }

  @override
  Future<UserModel?> getCachedUser() async {
    try {
      final userData = storageService.getAuthData<Map<String, dynamic>>(
        AppConfig.userDataKey,
      );
      
      if (userData != null) {
        return UserModel.fromJson(userData);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      return await storageService.readSecure(AppConfig.accessTokenKey);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return await storageService.readSecure(AppConfig.refreshTokenKey);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    try {
      final accessToken = await getAccessToken();
      final user = await getCachedUser();
      return accessToken != null && user != null;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      // Clear secure storage
      await storageService.deleteSecure(AppConfig.accessTokenKey);
      await storageService.deleteSecure(AppConfig.refreshTokenKey);

      // Clear auth box
      await storageService.clearAuthData();
    } catch (e) {
      throw Exception('Failed to clear auth data: $e');
    }
  }

  @override
  Future<void> updateUser(UserModel user) async {
    try {
      await storageService.saveAuthData(
        AppConfig.userDataKey,
        user.toJson(),
      );
    } catch (e) {
      throw Exception('Failed to update user: $e');
    }
  }

  @override
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      await storageService.saveAuthData(
        AppConfig.biometricEnabledKey,
        enabled,
      );
    } catch (e) {
      throw Exception('Failed to set biometric preference: $e');
    }
  }

  @override
  Future<bool> isBiometricEnabled() async {
    try {
      return storageService.getAuthData<bool>(AppConfig.biometricEnabledKey) ?? false;
    } catch (e) {
      return false;
    }
  }
}
