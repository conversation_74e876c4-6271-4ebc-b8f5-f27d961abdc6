import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/repositories/property_repository.dart';
import '../datasources/property_remote_datasource.dart';
import '../datasources/property_local_datasource.dart';
import '../models/property_model.dart';

class PropertyRepositoryImpl implements PropertyRepository {
  final PropertyRemoteDataSource remoteDataSource;
  final PropertyLocalDataSource localDataSource;

  PropertyRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, List<PropertyModel>>> getProperties(
    int page,
    int limit,
    String? search,
  ) async {
    try {
      final properties = await remoteDataSource.getProperties(page, limit, search);
      await localDataSource.cacheProperties(properties);
      return Right(properties);
    } on ServerException catch (e) {
      // Try to get cached data
      final cachedProperties = await localDataSource.getCachedProperties();
      if (cachedProperties.isNotEmpty) {
        return Right(cachedProperties);
      }
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      // Try to get cached data
      final cachedProperties = await localDataSource.getCachedProperties();
      if (cachedProperties.isNotEmpty) {
        return Right(cachedProperties);
      }
      return Left(NetworkFailure(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get properties: $e'));
    }
  }

  @override
  Future<Either<Failure, PropertyModel>> getPropertyDetails(String propertyId) async {
    try {
      final property = await remoteDataSource.getPropertyDetails(propertyId);
      await localDataSource.cacheProperty(property);
      return Right(property);
    } on ServerException catch (e) {
      // Try to get cached data
      final cachedProperty = await localDataSource.getCachedProperty(propertyId);
      if (cachedProperty != null) {
        return Right(cachedProperty);
      }
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      // Try to get cached data
      final cachedProperty = await localDataSource.getCachedProperty(propertyId);
      if (cachedProperty != null) {
        return Right(cachedProperty);
      }
      return Left(NetworkFailure(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get property details: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getPropertyStatus(String propertyId) async {
    try {
      final status = await remoteDataSource.getPropertyStatus(propertyId);
      return Right(status);
    } on ServerException catch (e) {
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get property status: $e'));
    }
  }
}
