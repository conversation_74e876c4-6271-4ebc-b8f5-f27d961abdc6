import 'dart:async';
import 'package:flutter/foundation.dart';

import '../config/app_config.dart';
import 'connectivity_service.dart';
import 'storage_service.dart';

class SyncService {
  final ConnectivityService _connectivityService;
  final StorageService _storageService;
  
  Timer? _syncTimer;
  bool _isSyncing = false;
  
  final StreamController<SyncStatus> _syncStatusController = 
      StreamController<SyncStatus>.broadcast();

  Stream<SyncStatus> get syncStatus => _syncStatusController.stream;

  SyncService({
    ConnectivityService? connectivityService,
    StorageService? storageService,
  }) : _connectivityService = connectivityService ?? ConnectivityService(),
       _storageService = storageService ?? StorageService();

  void initialize() {
    // Listen to connectivity changes
    _connectivityService.connectionStatus.listen((isConnected) {
      if (isConnected && !_isSyncing) {
        _performSync();
      }
    });

    // Start periodic sync
    _startPeriodicSync();
  }

  void _startPeriodicSync() {
    _syncTimer = Timer.periodic(
      const Duration(minutes: AppConfig.syncIntervalMinutes),
      (_) {
        if (_connectivityService.isConnected && !_isSyncing) {
          _performSync();
        }
      },
    );
  }

  Future<void> _performSync() async {
    if (_isSyncing) return;

    _isSyncing = true;
    _syncStatusController.add(SyncStatus.syncing);

    try {
      // Sync pending data to server
      await _syncPendingData();
      
      // Fetch latest data from server
      await _fetchLatestData();
      
      _syncStatusController.add(SyncStatus.completed);
    } catch (e) {
      debugPrint('Sync failed: $e');
      _syncStatusController.add(SyncStatus.failed);
    } finally {
      _isSyncing = false;
    }
  }

  Future<void> _syncPendingData() async {
    // TODO: Implement syncing of pending data to server
    // This would include:
    // - Pending fuel updates
    // - Pending maintenance reports
    // - Pending attendance records
    // - Pending images
    
    await Future.delayed(const Duration(seconds: 1)); // Placeholder
  }

  Future<void> _fetchLatestData() async {
    // TODO: Implement fetching latest data from server
    // This would include:
    // - Property updates
    // - New maintenance issues
    // - System notifications
    // - User profile changes
    
    await Future.delayed(const Duration(seconds: 1)); // Placeholder
  }

  Future<void> forcSync() async {
    if (_connectivityService.isConnected) {
      await _performSync();
    } else {
      _syncStatusController.add(SyncStatus.noConnection);
    }
  }

  Future<void> queueForSync(String dataType, Map<String, dynamic> data) async {
    // Store data locally for later sync
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final key = '${dataType}_$timestamp';
    
    await _storageService.saveAuthData('pending_sync_$key', {
      'type': dataType,
      'data': data,
      'timestamp': timestamp,
      'retryCount': 0,
    });
  }

  Future<List<Map<String, dynamic>>> getPendingSyncItems() async {
    // TODO: Implement getting pending sync items from storage
    return [];
  }

  Future<void> clearPendingSyncItems() async {
    // TODO: Implement clearing pending sync items
  }

  Future<SyncStats> getSyncStats() async {
    final pendingItems = await getPendingSyncItems();
    final lastSyncTime = _storageService.getAuthData<int>('last_sync_time');
    
    return SyncStats(
      pendingItems: pendingItems.length,
      lastSyncTime: lastSyncTime != null 
          ? DateTime.fromMillisecondsSinceEpoch(lastSyncTime)
          : null,
      isConnected: _connectivityService.isConnected,
      isSyncing: _isSyncing,
    );
  }

  void dispose() {
    _syncTimer?.cancel();
    _syncStatusController.close();
  }
}

enum SyncStatus {
  idle,
  syncing,
  completed,
  failed,
  noConnection,
}

class SyncStats {
  final int pendingItems;
  final DateTime? lastSyncTime;
  final bool isConnected;
  final bool isSyncing;

  SyncStats({
    required this.pendingItems,
    this.lastSyncTime,
    required this.isConnected,
    required this.isSyncing,
  });

  String get statusText {
    if (isSyncing) return 'Syncing...';
    if (!isConnected) return 'No connection';
    if (pendingItems > 0) return '$pendingItems items pending';
    if (lastSyncTime != null) {
      final diff = DateTime.now().difference(lastSyncTime!);
      if (diff.inMinutes < 1) return 'Synced just now';
      if (diff.inHours < 1) return 'Synced ${diff.inMinutes}m ago';
      if (diff.inDays < 1) return 'Synced ${diff.inHours}h ago';
      return 'Synced ${diff.inDays}d ago';
    }
    return 'Never synced';
  }
}
