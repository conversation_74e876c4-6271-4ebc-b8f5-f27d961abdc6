import '../../../../core/services/storage_service.dart';
import '../models/property_model.dart';

abstract class PropertyLocalDataSource {
  Future<void> cacheProperties(List<PropertyModel> properties);
  Future<List<PropertyModel>> getCachedProperties();
  Future<void> cacheProperty(PropertyModel property);
  Future<PropertyModel?> getCachedProperty(String propertyId);
  Future<void> clearPropertyCache();
}

class PropertyLocalDataSourceImpl implements PropertyLocalDataSource {
  final StorageService storageService;

  PropertyLocalDataSourceImpl(this.storageService);

  @override
  Future<void> cacheProperties(List<PropertyModel> properties) async {
    try {
      final propertiesMap = <String, dynamic>{};
      for (final property in properties) {
        propertiesMap[property.id] = property.toJson();
      }
      await storageService.saveProperties(propertiesMap);
    } catch (e) {
      throw Exception('Failed to cache properties: $e');
    }
  }

  @override
  Future<List<PropertyModel>> getCachedProperties() async {
    try {
      final propertiesData = storageService.getAllProperties<Map<String, dynamic>>();
      return propertiesData.map((data) => PropertyModel.fromJson(data)).toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> cacheProperty(PropertyModel property) async {
    try {
      await storageService.saveProperty(property.id, property.toJson());
    } catch (e) {
      throw Exception('Failed to cache property: $e');
    }
  }

  @override
  Future<PropertyModel?> getCachedProperty(String propertyId) async {
    try {
      final propertyData = storageService.getProperty<Map<String, dynamic>>(propertyId);
      if (propertyData != null) {
        return PropertyModel.fromJson(propertyData);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearPropertyCache() async {
    try {
      await storageService.clearProperties();
    } catch (e) {
      throw Exception('Failed to clear property cache: $e');
    }
  }
}
