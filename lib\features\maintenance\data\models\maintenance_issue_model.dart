import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'maintenance_issue_model.g.dart';

@JsonSerializable()
class MaintenanceIssueModel extends Equatable {
  final String id;
  final String propertyId;
  final String title;
  final String description;
  final String priority;
  final String status;
  final String category;
  final String reportedBy;
  final String? assignedTo;
  final DateTime reportedAt;
  final DateTime? scheduledDate;
  final DateTime? completedAt;
  final List<String>? images;
  final String? notes;
  final double? estimatedCost;
  final double? actualCost;
  final Map<String, dynamic>? metadata;

  const MaintenanceIssueModel({
    required this.id,
    required this.propertyId,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    required this.category,
    required this.reportedBy,
    this.assignedTo,
    required this.reportedAt,
    this.scheduledDate,
    this.completedAt,
    this.images,
    this.notes,
    this.estimatedCost,
    this.actualCost,
    this.metadata,
  });

  factory MaintenanceIssueModel.fromJson(Map<String, dynamic> json) =>
      _$MaintenanceIssueModelFromJson(json);

  Map<String, dynamic> toJson() => _$MaintenanceIssueModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        propertyId,
        title,
        description,
        priority,
        status,
        category,
        reportedBy,
        assignedTo,
        reportedAt,
        scheduledDate,
        completedAt,
        images,
        notes,
        estimatedCost,
        actualCost,
        metadata,
      ];
}
