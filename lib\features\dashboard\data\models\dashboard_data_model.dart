import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'dashboard_data_model.g.dart';

@JsonSerializable()
class DashboardDataModel extends Equatable {
  final int totalProperties;
  final int activeIssues;
  final int fuelAlerts;
  final int completedTasks;
  final List<RecentActivityModel> recentActivities;
  final Map<String, dynamic> stats;

  const DashboardDataModel({
    required this.totalProperties,
    required this.activeIssues,
    required this.fuelAlerts,
    required this.completedTasks,
    required this.recentActivities,
    required this.stats,
  });

  factory DashboardDataModel.fromJson(Map<String, dynamic> json) =>
      _$DashboardDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardDataModelToJson(this);

  @override
  List<Object?> get props => [
        totalProperties,
        activeIssues,
        fuelAlerts,
        completedTasks,
        recentActivities,
        stats,
      ];
}

@JsonSerializable()
class RecentActivityModel extends Equatable {
  final String id;
  final String title;
  final String description;
  final String type;
  final DateTime timestamp;
  final String? propertyId;
  final String? userId;

  const RecentActivityModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.timestamp,
    this.propertyId,
    this.userId,
  });

  factory RecentActivityModel.fromJson(Map<String, dynamic> json) =>
      _$RecentActivityModelFromJson(json);

  Map<String, dynamic> toJson() => _$RecentActivityModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        type,
        timestamp,
        propertyId,
        userId,
      ];
}
