import '../../../../core/services/storage_service.dart';
import '../models/fuel_status_model.dart';
import '../models/fuel_update_request_model.dart';

abstract class FuelLocalDataSource {
  Future<void> cacheFuelStatus(FuelStatusModel fuelStatus);
  Future<FuelStatusModel?> getCachedFuelStatus(String propertyId);
  Future<void> cacheFuelHistory(String propertyId, List<FuelStatusModel> history);
  Future<List<FuelStatusModel>> getCachedFuelHistory(String propertyId);
  Future<void> savePendingFuelUpdate(FuelUpdateRequestModel request);
  Future<List<FuelUpdateRequestModel>> getPendingFuelUpdates();
  Future<void> removePendingFuelUpdate(String requestId);
  Future<void> clearFuelCache();
}

class FuelLocalDataSourceImpl implements FuelLocalDataSource {
  final StorageService storageService;

  FuelLocalDataSourceImpl(this.storageService);

  @override
  Future<void> cacheFuelStatus(FuelStatusModel fuelStatus) async {
    try {
      await storageService.saveFuelData(
        'status_${fuelStatus.propertyId}',
        fuelStatus.toJson(),
      );
    } catch (e) {
      throw Exception('Failed to cache fuel status: $e');
    }
  }

  @override
  Future<FuelStatusModel?> getCachedFuelStatus(String propertyId) async {
    try {
      final data = storageService.getFuelData<Map<String, dynamic>>('status_$propertyId');
      if (data != null) {
        return FuelStatusModel.fromJson(data);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> cacheFuelHistory(String propertyId, List<FuelStatusModel> history) async {
    try {
      final historyData = history.map((item) => item.toJson()).toList();
      await storageService.saveFuelData('history_$propertyId', historyData);
    } catch (e) {
      throw Exception('Failed to cache fuel history: $e');
    }
  }

  @override
  Future<List<FuelStatusModel>> getCachedFuelHistory(String propertyId) async {
    try {
      final data = storageService.getFuelData<List<dynamic>>('history_$propertyId');
      if (data != null) {
        return data
            .cast<Map<String, dynamic>>()
            .map((item) => FuelStatusModel.fromJson(item))
            .toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> savePendingFuelUpdate(FuelUpdateRequestModel request) async {
    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      await storageService.saveFuelData(
        'pending_$timestamp',
        request.toJson(),
      );
    } catch (e) {
      throw Exception('Failed to save pending fuel update: $e');
    }
  }

  @override
  Future<List<FuelUpdateRequestModel>> getPendingFuelUpdates() async {
    try {
      final allData = storageService.getAllFuelData<Map<String, dynamic>>();
      final pendingUpdates = allData
          .where((data) => data.containsKey('propertyId'))
          .map((data) => FuelUpdateRequestModel.fromJson(data))
          .toList();
      return pendingUpdates;
    } catch (e) {
      return [];
    }
  }

  @override
  Future<void> removePendingFuelUpdate(String requestId) async {
    try {
      await storageService.deleteFuelData(requestId);
    } catch (e) {
      throw Exception('Failed to remove pending fuel update: $e');
    }
  }

  @override
  Future<void> clearFuelCache() async {
    try {
      await storageService.clearFuelData();
    } catch (e) {
      throw Exception('Failed to clear fuel cache: $e');
    }
  }
}
