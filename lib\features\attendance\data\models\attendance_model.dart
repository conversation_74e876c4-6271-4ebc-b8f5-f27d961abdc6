import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'attendance_model.g.dart';

@JsonSerializable()
class AttendanceModel extends Equatable {
  final String id;
  final String userId;
  final String siteId;
  final DateTime checkInTime;
  final DateTime? checkOutTime;
  final double? checkInLatitude;
  final double? checkInLongitude;
  final double? checkOutLatitude;
  final double? checkOutLongitude;
  final String? notes;
  final String status;
  final int? workingHours;
  final Map<String, dynamic>? metadata;

  const AttendanceModel({
    required this.id,
    required this.userId,
    required this.siteId,
    required this.checkInTime,
    this.checkOutTime,
    this.checkInLatitude,
    this.checkInLongitude,
    this.checkOutLatitude,
    this.checkOutLongitude,
    this.notes,
    required this.status,
    this.workingHours,
    this.metadata,
  });

  factory AttendanceModel.fromJson(Map<String, dynamic> json) =>
      _$AttendanceModelFromJson(json);

  Map<String, dynamic> toJson() => _$AttendanceModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        userId,
        siteId,
        checkInTime,
        checkOutTime,
        checkInLatitude,
        checkInLongitude,
        checkOutLatitude,
        checkOutLongitude,
        notes,
        status,
        workingHours,
        metadata,
      ];
}
