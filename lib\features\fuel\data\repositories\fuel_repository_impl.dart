import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/error/exceptions.dart';
import '../../domain/repositories/fuel_repository.dart';
import '../datasources/fuel_remote_datasource.dart';
import '../datasources/fuel_local_datasource.dart';
import '../models/fuel_status_model.dart';
import '../models/fuel_update_request_model.dart';

class FuelRepositoryImpl implements FuelRepository {
  final FuelRemoteDataSource remoteDataSource;
  final FuelLocalDataSource localDataSource;

  FuelRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<Either<Failure, FuelStatusModel>> getFuelStatus(String propertyId) async {
    try {
      final fuelStatus = await remoteDataSource.getFuelStatus(propertyId);
      await localDataSource.cacheFuelStatus(fuelStatus);
      return Right(fuelStatus);
    } on ServerException catch (e) {
      // Try to get cached data
      final cachedStatus = await localDataSource.getCachedFuelStatus(propertyId);
      if (cachedStatus != null) {
        return Right(cachedStatus);
      }
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      // Try to get cached data
      final cachedStatus = await localDataSource.getCachedFuelStatus(propertyId);
      if (cachedStatus != null) {
        return Right(cachedStatus);
      }
      return Left(NetworkFailure(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get fuel status: $e'));
    }
  }

  @override
  Future<Either<Failure, FuelStatusModel>> updateFuelLevel(
    FuelUpdateRequestModel request,
  ) async {
    try {
      final updatedStatus = await remoteDataSource.updateFuelLevel(request);
      await localDataSource.cacheFuelStatus(updatedStatus);
      return Right(updatedStatus);
    } on ServerException catch (e) {
      // Save for later sync
      await localDataSource.savePendingFuelUpdate(request);
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      // Save for later sync
      await localDataSource.savePendingFuelUpdate(request);
      return Left(NetworkFailure(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to update fuel level: $e'));
    }
  }

  @override
  Future<Either<Failure, List<FuelStatusModel>>> getFuelHistory(
    String propertyId,
    int page,
    int limit,
  ) async {
    try {
      final history = await remoteDataSource.getFuelHistory(propertyId, page, limit);
      await localDataSource.cacheFuelHistory(propertyId, history);
      return Right(history);
    } on ServerException catch (e) {
      // Try to get cached data
      final cachedHistory = await localDataSource.getCachedFuelHistory(propertyId);
      if (cachedHistory.isNotEmpty) {
        return Right(cachedHistory);
      }
      return Left(ServerFailure(message: e.message, statusCode: e.statusCode));
    } on NetworkException catch (e) {
      // Try to get cached data
      final cachedHistory = await localDataSource.getCachedFuelHistory(propertyId);
      if (cachedHistory.isNotEmpty) {
        return Right(cachedHistory);
      }
      return Left(NetworkFailure(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to get fuel history: $e'));
    }
  }

  @override
  Future<Either<Failure, List<FuelUpdateRequestModel>>> getPendingUpdates() async {
    try {
      final pendingUpdates = await localDataSource.getPendingFuelUpdates();
      return Right(pendingUpdates);
    } catch (e) {
      return Left(StorageFailure(message: 'Failed to get pending updates: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> syncPendingUpdates() async {
    try {
      final pendingUpdates = await localDataSource.getPendingFuelUpdates();
      
      for (final update in pendingUpdates) {
        try {
          await remoteDataSource.updateFuelLevel(update);
          // Remove from pending after successful sync
          await localDataSource.removePendingFuelUpdate(update.propertyId);
        } catch (e) {
          // Continue with other updates even if one fails
          continue;
        }
      }
      
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure(message: 'Failed to sync pending updates: $e'));
    }
  }
}
