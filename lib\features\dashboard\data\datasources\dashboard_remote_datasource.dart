import '../../../../core/network/api_client.dart';
import '../models/dashboard_data_model.dart';

abstract class DashboardRemoteDataSource {
  Future<DashboardDataModel> getDashboardData();
  Future<Map<String, dynamic>> getDashboardAnalytics();
}

class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final ApiClient apiClient;

  DashboardRemoteDataSourceImpl(this.apiClient);

  @override
  Future<DashboardDataModel> getDashboardData() async {
    try {
      return await apiClient.getDashboardData();
    } catch (e) {
      throw Exception('Failed to get dashboard data: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getDashboardAnalytics() async {
    try {
      return await apiClient.getDashboardAnalytics();
    } catch (e) {
      throw Exception('Failed to get dashboard analytics: $e');
    }
  }
}
