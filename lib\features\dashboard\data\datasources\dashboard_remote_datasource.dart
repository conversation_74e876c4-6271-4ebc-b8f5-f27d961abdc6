import '../models/dashboard_data_model.dart';

abstract class DashboardRemoteDataSource {
  Future<DashboardDataModel> getDashboardData();
  Future<Map<String, dynamic>> getDashboardAnalytics();
}

class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  DashboardRemoteDataSourceImpl();

  @override
  Future<DashboardDataModel> getDashboardData() async {
    try {
      // Mock dashboard data for demo purposes
      await Future.delayed(const Duration(seconds: 1));

      return DashboardDataModel(
        totalProperties: 12,
        activeIssues: 3,
        fuelAlerts: 2,
        completedTasks: 8,
        recentActivities: [
          RecentActivityModel(
            id: '1',
            title: 'Fuel Updated',
            description: 'Fuel level updated at Property A',
            type: 'fuel',
            timestamp: DateTime.now().subtract(const Duration(hours: 2)),
            propertyId: 'prop1',
            userId: 'user1',
          ),
          RecentActivityModel(
            id: '2',
            title: 'Maintenance Completed',
            description: 'Generator maintenance completed',
            type: 'maintenance',
            timestamp: DateTime.now().subtract(const Duration(hours: 4)),
            propertyId: 'prop2',
            userId: 'user2',
          ),
        ],
        stats: {
          'totalRevenue': 125000,
          'monthlyGrowth': 12.5,
          'activeContracts': 45,
        },
      );
    } catch (e) {
      throw Exception('Failed to get dashboard data: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> getDashboardAnalytics() async {
    try {
      // Mock analytics data for demo purposes
      await Future.delayed(const Duration(milliseconds: 500));

      return {
        'fuelConsumption': {
          'daily': [120, 135, 98, 145, 167, 134, 156],
          'weekly': [890, 945, 1023, 987],
          'monthly': [3456, 3789, 4123, 3987],
        },
        'maintenanceStats': {
          'completed': 45,
          'pending': 12,
          'overdue': 3,
        },
        'propertyStatus': {
          'active': 10,
          'maintenance': 2,
          'offline': 0,
        },
      };
    } catch (e) {
      throw Exception('Failed to get dashboard analytics: $e');
    }
  }
}
